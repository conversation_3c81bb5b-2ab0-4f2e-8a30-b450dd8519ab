export interface ISubscriptionPackageInfo {
  title: string;
  name: string;
  type: string;
}

export interface ISubscriptionUserInfo {
  name: string;
  email: string;
}
export interface ISubscriptionList {
  userId: string;
  packageId: string;
  expireAt: Date;
  createdAt: string;
  packageInfo: ISubscriptionPackageInfo;
  userInfo: ISubscriptionUserInfo;
}

export interface ISubscription {
  id?: string;
  userId: string;
  packageId: string;
  productIdName: string;
  isActive: boolean;
  price: number;
  isPaid: boolean;
  paymentType: PackagePaymentTypeEnum;
  paidAt: Date;
  expireAt: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum PackageFeatureEnum {
  SPOT_REQUEST = 'SPOT_REQUEST',
  PROFILE_VIEW = 'PROFILE_VIEW',
}

export enum PackagePaymentTypeEnum {
  GOOGLE = 'GOOGLE',
  APPLE = 'APPLE',
}
export interface ISubscriptionReq {
  transactionId?: string;
  paymentType: PackagePaymentTypeEnum;
  googlePaymentToken?: string;
  packageId: string;
  packageName?: string;
  userId?: string;
}

export interface IAppleTrx {
  userId?: string;
  transactionId: string;
  productId: string;
}

export enum SubscriptionResEnum {
  PAYMENT_NOT_VERIFIED = 'Payment not verified',
  SUBSCRIPTION_COMPLETED = 'Subscription completed',
  ERROR_IN_COMPLETING_SUBSCRIPTION = 'Error in completing subscription',
  ERROR_IN_COMPLETING_APPLE_AUTO_RENEW = 'Error in completing apple auto renewal',
  ERROR_IN_CREATING_APPLE_TRX_ID = 'Error in apple pay transaction id',
  YOU_HAVE_NO_SUBSCRIBED_PACKAGE = 'You have no subscribed package',
  DAILY_LIMIT_REACHED = 'Daily limit reached!',
  ERROR_IN_VERIFYING_PACKAGE = 'Error in verifying package',
  FREE_TRIAL_IS_OVER = 'Your free trial is over',
}
