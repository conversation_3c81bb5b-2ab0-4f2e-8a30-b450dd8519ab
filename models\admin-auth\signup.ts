import { SuccessResponse } from '../common/index';

/**
 * API Path: /admin-auth/signup
 * method: POST
 * body: CreateAdminRequest
 * response: CreateAdminSuccessResponse
 */

export interface CreateAdminRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

export const enum SignUpSuccessMessages {
  ADMIN_SIGNUP_SUCCESSFUL = 'Admin signup successful',
}

export interface CreateAdminSuccessResponse extends SuccessResponse {
  data: {
    message?: SignUpSuccessMessages;
  };
}

export const enum SignUpErrorMessages {
  ADMIN_ALREADY_EXITS = 'Admin already exist',
  CAN_NOT_CREATE_ADMIN = 'Can not create admin',
}
