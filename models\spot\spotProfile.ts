import { Image } from 'models/club';

export enum SpotProfileGenderEnum {
  MALE = 'male',
  FEMALE = 'female',
  PREFER_NOT_SAY = 'prefer not say',
}

export enum WeightSpotUserEnum {
  LB = 'lb',
  KG = 'kg',
}

export interface SpotProfileImage {
  id?: string;
  url: string;
}
export interface ISpotNot {
  userId: string;
  expireDate: Date;
}
export interface SpotProfile {
  id?: string;
  bio?: string;
 
  profession?: {
    company?: string;
    position?: string;
  };
  userId: string;
  gender: string;
  name: string;
  age: number;
  workoutType?: string;
  interest?: string[];
  
  education?: {
    school?: string;
    college?: string;
    university?: string;
  };
  bodyType?: string;
  clubId?: string;
  images?: SpotProfileImage[];
  maxBench?: number;
  maxSquat: number;
  weightType?: string;
  location?: {
    type: string;
    coordinates: number[];
  };
  ghostModeOn?: boolean;
  spotNot?: ISpotNot[];
  isDeleted?: boolean;
  clubInfo?: {
    id?: string;
    name: string;
    image: Image;
  };
  isOline?: boolean;
  address?:{
    district: string;
    country: string;
  }
}
