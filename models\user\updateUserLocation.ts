import { SuccessResponse } from '../common/index';
import { User } from './user';

/**
 * API Path: /user/update-location
 * method: PATCH
 * body: UpdateUserLocationRequestBody
 * response: UpdateUserLocationResponse
 */

export interface UpdateUserLocationRequestBody {
  longitude: number;
  latitude: number;
}

export interface UpdateUserLocationSuccessResponse extends SuccessResponse {
  data: User;
}

export const enum UpdateUserLocationErrorMessages {
  USER_LOCATION_UPDATED_FAILED = 'Can not update user location',
  INVALID_LOCATION_POINTS = 'Invalid location points',
}
