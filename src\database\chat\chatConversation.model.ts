import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { ChatConversation } from 'src/entity/chatConversation';

const ChatConversationSchema = new Schema<ChatConversation>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    users: {
      type: [String],
      index: true,
    },
    type: {
      type: String,
      enum: ['onetoone', 'group'],
      default: 'onetoone',
    },
    lastMessageId: {
      type: String,
      default: '',
    },
    groupId: {
      type: String,
      default: null,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const ChatConversationModel = model<ChatConversation>(
  'chat-conversation',
  ChatConversationSchema,
);
export { ChatConversationModel };
