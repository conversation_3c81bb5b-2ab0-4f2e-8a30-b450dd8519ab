import { SuccessResponse } from '../common/index';

export interface AddProductAnswerParams {
  productId: string;
  questionId: string;
}

export interface AddProductAnswerRequest {
  answer: string;
}

export const enum AddProductAnswerSuccessMessages {
  ANSWER_SUCCESSFULLY_ADDED = 'Answer successfully added',
}

export interface AddProductAnswerSuccessResponse extends SuccessResponse {
  data: {
    message: AddProductAnswerSuccessMessages.ANSWER_SUCCESSFULLY_ADDED;
  };
}

export const enum AddProductAnswerErrorMessages {
  INVALID_PRODUCT_ID = 'Invalid product Id',
  CAN_NOT_ADD_ANSWER = 'Can not add answer',
}
