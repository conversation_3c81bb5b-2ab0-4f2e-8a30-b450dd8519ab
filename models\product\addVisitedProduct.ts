import { SuccessResponse } from '../common/index';

export interface AddVisitedProductRequest {
  productId: string;
}

interface AddVisitedProductResponseMessage {
  message: string;
}

export interface AddVisitedProductRequestResponse extends SuccessResponse {
  data: AddVisitedProductResponseMessage;
}

export const enum AddVisitedProductMessages {
  VISITED_PRODUCT_ADDED_SUCCESSFUL = 'Visited product added successfully',
  INVALID_PRODUCT_ID = 'Invalid Product Id',
  CANNOT_ADD_VISITED_PRODUCT = 'Can not add visited product',
}
