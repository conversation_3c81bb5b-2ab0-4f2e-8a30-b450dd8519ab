export interface IUserPoints {
  id?: string;
  userId: string;
  points: number;
  userRanking?: number;
  totalRanking?: number;
  conversionRate?: number;
  amountToWithdraw?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum IUserPointsTransTypeEnum {
  EARNED = 'earned',
  SPENT = 'spent',
}

export enum IUserPointsSources {
  Challenges = 'challenges',
  Referrals='referrals',
  DailyTasks = 'daily_tasks',
  DailySteaks = 'daily_streaks',

}

export interface IUserPointsTransHistory {
  id?: string;
  userId: string;
  type: IUserPointsTransTypeEnum;
  pointSourceType?: IUserPointsSources;
  pointSourceId?: string;
  pointSourceName?: string;
  points: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUpdatePointsList {
  type: IUserPointsTransTypeEnum;
  pointSourceType?: IUserPointsSources;
  pointSourceId?: string;
  pointSourceName?: string;
  points: number;
}

export interface IUpdatePoints {
  type: IUserPointsTransTypeEnum;
  points: number;
}

export interface IUserLeadersboard {
  totalPoints: number;
  userId: string;
  name: string;
  image: string;
}

export interface IUserPointsTransHistory {
  id?: string;
  userId: string;
  type: IUserPointsTransTypeEnum;
  pointSourceType?: IUserPointsSources;
  pointSourceId?: string;
  points: number;
  createdAt?: Date;
  updatedAt?: Date;
}
