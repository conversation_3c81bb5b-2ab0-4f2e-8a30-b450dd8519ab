import { Brand } from './brand';
import { SuccessResponse } from '../common/successResponse';
import { BrandInfo, BrandMeta } from './createBrand';

export interface UpdateBrandRequest {
  info?: BrandUpdateInfo;
  meta?: BrandMeta;
}

export interface BrandUpdateInfo {
  description?: string;
  allowToSelectPageSize?: boolean;
  published?: boolean;
  displayOrder?: number;
  pageSizeOptions?: number[];
}
export interface UpdatedBrand {
  id: string;
  info: BrandInfo;
  meta: BrandMeta;
}

export interface UpdateBrandSuccessResponse extends SuccessResponse {
  data: Brand;
}

export const enum UpdateBrandErrorMessageUpdate {
  INVALID_BRAND_ID = 'Invalid brand id',
  CANNOT_UPDATE_BRAND = 'Can not update brand',
  BRAND_ALREADY_EXISTS = 'Brand already exists',
  INFO_OR_META_OBJECT_MISSING = 'Info or meta object missing',
}
