import { SuccessResponse } from 'models/common';
import { ClubMember, ClubMemberStatus } from './clubMember';

/**
 * API Path: /api/clubs/{clubId}/members/list
 * method: GET
 * Param: FilteredMemberListParam
 * Query Parameter: FilteredMemberListQuery
 * response: FilteredMemberListResponse
 */

export interface FilteredMemberListParam {
  clubId: string;
}

export interface FilteredMemberListQuery {
  status?: ClubMemberStatus;
}

export interface FilteredMemberListSuccessResponse extends SuccessResponse {
  data: ClubMember[];
}

export const enum FilteredMemberListErrorMessages {
  NO_CLUB_EXIST = 'No club exist',
  CAN_NOT_GET_CLUB_MEMBER_LIST = 'Can not get club member list',
}
