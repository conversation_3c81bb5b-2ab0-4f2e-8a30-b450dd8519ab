import { SuccessResponse } from '../common/index';
import { chatbotSession } from './chatbot';

export interface GetChatbotSuccessResponse extends SuccessResponse {
  data: chatbotSession;
}

export interface GetChatbotListSuccessResponse extends SuccessResponse {
  data: chatbotSession[];
}

export interface GetChatbotSuccessResponseNull extends SuccessResponse {
  data: chatbotSession;
}

export interface ChatbotSessionListItem {
  id: string;
  lastUserMessage: {
    content: string;
    createdAt: Date;
  };
}

export interface ChatbotSessionListResponse {
  userId: string;
  sessions: ChatbotSessionListItem[];
}

export interface GetChatbotSessionListResponse extends SuccessResponse {
  data: ChatbotSessionListResponse | null;
}

export const enum GetChatbotSessionErrorMessages {
  CAN_NOT_FIND_SESSION = 'Can not find Session',
}

export const enum DeleteChatbotHistorySuccessMessages {
  HISTORY_DELETED_SUCCESSFULLY = 'History deleted successfully',
}

export const enum DeleteChatbotHistoryErrorMessages {
  CAN_NOT_FIND_CHAT_HISTORY = 'Can not find chat history',
}
