name: Fitsomnia Staging Server Deployment

on:
  push:
    branches:
      - development

jobs:
  deploy:
    name: Deploy Staging Server
    permissions:
      deployments: write
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: 'actions/checkout@v2'
        with:
          ref: development
          token: ${{ secrets.PERSONAL_GITHUB_TOKEN }}
      - name: Create GitHub deployment
        uses: chrnorm/deployment-action@v2
        id: deployment
        with:
          token: '${{ github.token }}'
          environment-url: ${{ vars.STAGING_SERVER_URL }}
          environment: staging
      - name: Set up SSH Key and Deploy my App on Server
        # run: |
        uses: appleboy/ssh-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.PERSONAL_GITHUB_TOKEN }}
        with:
          host: ${{ secrets.STAGING_SERVER_IP }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.STAGING_SERVER_PRIVATE_KEY }}
          port: 22
          script: |
            git config --global url."https://${{ secrets.PERSONAL_GITHUB_TOKEN }}:@github.com".insteadOf "https://github.com"
            cd /var/www/fitsomnia/fitsomnia-backend
            git pull
            git checkout development
            export NVM_DIR=~/.nvm
            source ~/.nvm/nvm.sh
            nvm use 22
            export PATH=$HOME/.yarn/bin:$PATH
            node -v
            yarn -v
            yarn install
            yarn build
            pm2 restart backend --update-env
      - name: Update deployment Status (success)
        if: success()
        uses: chrnorm/deployment-status@v2
        with:
          token: '${{ github.token }}'
          environment-url: ${{ vars.STAGING_SERVER_URL }}
          state: 'success'
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
      
      - name: Update deployment status (failure)
        if: failure()
        uses: chrnorm/deployment-status@v2
        with:
          token: '${{ github.token }}'
          environment-url: ${{ vars.STAGING_SERVER_URL }}
          state: 'failure'
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
