import { model, Schema } from 'mongoose';
import { randomUUID } from 'crypto';
import { TrackShipment } from 'src/entity/shipment';

const ShippingSchema = new Schema<TrackShipment>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    shippingId: {
      type: String,
      required: true,
      index: true,
    },
    orderId: {
      type: String,
      required: true,
      index: true,
    },
    shipmentOrderId: {
      type: String,
      required: true,
      index: true,
    },
    rate: {
      type: Object,
      default: null,
    },
    tracker: {
      type: Object,
      default: null,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const ShippingModel = model<TrackShipment>('shipping', ShippingSchema);
export { ShippingModel };
