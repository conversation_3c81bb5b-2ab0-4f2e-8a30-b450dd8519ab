export interface IUserChallengeDetails {
  name: string;
  description: string;
  mechanics: string;
  type: string;
  category: string[];
  forceType: string[];
  primaryMuscles: string[];
  secondaryMuscles: string[];
  equipments: string[];
  preview: string;
  difficulty: string;
  duration: number;
  loop: number;
  points: number;
}

export interface IUserChallengeHistoryRes {
  lastStatus: string;
  startDate: Date;
  expireAt: Date;
  id: string;
  videoUrl?: string;
  challengeDetails: IUserChallengeDetails;
}

export interface IUserPointsHistory {
  points: number;
  lastStatus: string;
  name: string;
}
export interface IUserLeaderboard {
  totalPoints: number;
  userId: string;
  name: string;
  image: string;
}
