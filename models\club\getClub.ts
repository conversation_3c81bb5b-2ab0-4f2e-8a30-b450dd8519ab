import { SuccessResponse } from '../common/index';
import { Club } from './club';

/**
 * API Path: /api/clubs/{clubId}
 * method: GET
 * response: GetClubSuccessResponse
 */
export interface GetClubParam {
  clubId: string;
}
export interface GetClubSuccessResponse extends SuccessResponse {
  data: Club;
}

export const enum GetClubErrorMessages {
  NOT_A_MEMBER_OF_THIS_CLUB = 'Not a member of this club',
  CAN_NOT_GET_CLUB = 'Can not get club',
}
