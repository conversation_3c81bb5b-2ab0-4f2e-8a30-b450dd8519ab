import { ApiProperty } from "@nestjs/swagger";
import { Expose, Type } from "class-transformer";
import { IsArray, IsInt, IsNotEmpty, IsObject, IsOptional, IsString, Min } from "class-validator";
import { ServiceSuccessResponse } from "src/helper/serviceResponse/service.response.interface";

export class GetUserStreakHistoryQueryDto {

  @Expose()
  @ApiProperty({ required: false, type: String })
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty({ required: false, type: Number, description: 'How many items you want to skip' })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  offset?: number;

  @ApiProperty({ required: false, type: Number, description: 'How many items you want' })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number;
}

export class ActivitySessionDto {
  @Expose()
  @ApiProperty({ required: true, type: Date })
  startTime: Date;

  @Expose()
  @ApiProperty({ required: true, type: Date })
  endTime: Date;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  minutes: number;
}

export class UserDailyActivityDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  userId: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  date: string;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  profileViewCount: number;

  @Expose()
  @ApiProperty({ required: true, type: [String] })
  profileViewedIds: string[];

  @Expose()
  @ApiProperty({ required: true, type: Number })
  spotRequestCount: number;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  activityDuration: number;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  activityMetRequirement: boolean;

  @Expose()
  @ApiProperty({ required: true, type: [ActivitySessionDto] })
  activitySessionHistory: ActivitySessionDto[];

  @Expose()
  @ApiProperty({ required: false, type: Date })
  createdAt?: Date;

  @Expose()
  @ApiProperty({ required: false, type: Date })
  updatedAt?: Date;
}

export class GetUserStreakHistoryResponseDto {
  @Expose()
  @ApiProperty({ required: true, type: [UserDailyActivityDto] })
  @IsArray()
  @IsNotEmpty()
  activities: UserDailyActivityDto[];
}

export class GetUserStreakHistorySuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: GetUserStreakHistoryResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: GetUserStreakHistoryResponseDto;
}

// export class 