import { PaymentMethodEnum } from 'src/entity/order';
import { IOrderAddress } from './order.response.interface';

// create order request
export interface IPlaceOrderProductInfoReq {
  productId: string;
  photo?: string;
  quantity: number;
  size: string;
  color?: string;
}

export interface IPlaceOrderReq {
  products: IPlaceOrderProductInfoReq[];
  billingAddress: IOrderAddress;
  shippingAddress: IOrderAddress;
  paymentMethod: PaymentMethodEnum;
}

// create product
export interface IPlaceOrderProductInfo extends IPlaceOrderProductInfoReq {
  name: string;
  isRedeemable: boolean;
  price: number;
  totalPrice: number;
  sku: string;
  //parcel info
  parcel: {
    length?: number;
    height?: number;
    width?: number;
    weight: number;
  };
}

export interface IPlaceOrder extends IPlaceOrderReq {
  products: IPlaceOrderProductInfo[];
  billingAddress: IOrderAddress;
  shippingAddress: IOrderAddress;
  paymentMethod: PaymentMethodEnum;

  productCost: number;
  shippingCost: number;
  totalCost: number;
}

// create order response
export interface IPlaceOrderRes {
  userId: string;
  orderId: string;
  orderedDate: Date;
  orderStatus: string;
  shippingStatus: string;
  paymentStatus: string;
  products: IPlaceOrderProductInfo[];
  productCost: number;
  shippingCost: number;
  totalCost: number;
}
