export enum IChatConversationTypeEnum {
  ONETOONE = 'onetoone',
  GROUP = 'group',
}

export interface IChatConversation {
  id?: string;
  users: string[];
  groupId?: string | null;
  type?: IChatConversationTypeEnum;
  lastMessageId: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface IChatConversationRes {
  id?: string;
  users: string[];
  groupId?: string | null;
  type?: string;
  lastMessageId: string;
  createdAt?: string;
  updatedAt?: string;
}
