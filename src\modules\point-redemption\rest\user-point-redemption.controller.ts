import { Controller, Get, HttpStatus, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { User as UserInfo } from 'src/decorators/auth.decorator';
import { User } from 'src/entity/user';
import { GetPointsToMoneyResponseDtoSuccessResponseDto } from '../dtos/ge-points-to-money.dto';
import { GetRedemptionPointResponseDtoSuccessResponseDto } from '../dtos/get-redemption.dto';
import { PointRedemptionService } from '../services/point-redemption.service';

@ApiTags('Point Redemption API - User')
@ApiBearerAuth()
@UseGuards(new RolesGuard(['user']))
@Controller('user/point-redemption')
export class UserPointRedemptionController {
    constructor(private readonly pointRedemptionService: PointRedemptionService) { }

    @ApiOperation({ summary: 'Get active conversion rate configuration' })
    @Get('config')
    @ApiResponse({
        description: 'Get Active conversion rate response',
        type: GetRedemptionPointResponseDtoSuccessResponseDto,
        status: HttpStatus.OK,
    })
    async getActiveConfig() {
        return await this.pointRedemptionService.getActiveConfig();
    }

    @ApiOperation({ summary: 'Get money from points' })
    @Get('points-to-money')
    @ApiResponse({
        description: 'Get money from points response',
        type: GetPointsToMoneyResponseDtoSuccessResponseDto,
        status: HttpStatus.OK,
    })
    async getPointsToMoneyConversion(@UserInfo() user: User) {
        return await this.pointRedemptionService.getPointsToMoneyConversion(user.id);
    }
    
}
