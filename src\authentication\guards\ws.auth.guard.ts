import {
  CanActivate,
  ExecutionContext,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { authConfig } from 'config/auth';
import { APIException } from 'src/internal/exception/api.exception';
import { RedisDBService } from 'src/modules/chat/realtime/redis/redis.db.service';

@Injectable()
export class WsJwtGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private readonly redisDBService: RedisDBService,
  ) {}

  async canActivate(context: ExecutionContext) {
    try {
      console.log("ws guard");
      
      const client = context.switchToWs().getClient();
      const token: string = client.handshake?.query?.token.split(' ')[1];
      const user = this.jwtService.verify(token, {
        secret: authConfig.jwt_key,
      });
      if (user) {
        context.switchToWs().getData().userId = user.id;
        context.switchToWs().getData().clientId = client.id;
        const userInfo = {
          clientId: client.id,
          userId: user.id,
          isOnline: true,
        };
        await this.redisDBService.makeUserOnline(userInfo);
        
        await this.redisDBService.clientIdList();
        return true;
      }
      throw new APIException(
        'Sorry! You are not a valid user for this action',
        'BAD_REQUEST',
        HttpStatus.FORBIDDEN,
      );
    } catch (error) {
      console.log(error.message);
      throw new APIException(
        'Sorry! You are not a valid user for this action',
        'BAD_REQUEST',
        HttpStatus.FORBIDDEN,
      );
    }
  }
}
