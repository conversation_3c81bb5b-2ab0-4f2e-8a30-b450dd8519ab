import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { authConfig } from 'config/auth';
import { Request } from 'express';
import Redis from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { IUserSession } from 'models';
import { UserSessionModel } from 'src/database/session/user-session.model';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(@InjectRedis() private readonly redis: Redis) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        (request: Request) => {
          // console.log('request header jwt  bearer token', request?.headers?.authorization)
          const data =
            request?.headers?.authorization?.split(' ')[1] ||
            request?.cookies?.jwt?.split(' ')[1];

          
            
          if (!data) {
            return null;
          }

          
          return data;
        },
      ]),

     
      ignoreExpiration: false,
      secretOrKey: authConfig.jwt_key,
    });
  }

  async validate(payload: any) {

    // console.log('payload extracted from jwt token jwt-strategy page', payload)


    if (payload === null || !payload?.sessionId) {
      
      throw new UnauthorizedException(
        'Sorry! You are not a valid user for this action.',
      );
     
    }
    let session: IUserSession;
    const sessionCache = await this.redis.get(`USER_SESSION_${payload.id}`);
    if (!sessionCache) {
      // rebuild cache
      session = await UserSessionModel.findOne({
        userId: payload.id,
        sessionId: payload.sessionId,
        expireAt: { $gte: new Date() },
      }).lean();
      if (!session) {
        throw new UnauthorizedException('Session error!!');
      } else {
        await this.redis.set(
          `USER_SESSION_${payload.id}`,
          JSON.stringify(session),
        );
      }
    } else {
      session = JSON.parse(sessionCache);
    }

    if (
      new Date(session?.expireAt) > new Date() &&
      payload.sessionId === session?.sessionId
    ) {
      return payload;
    } else {
      throw new UnauthorizedException('Session error!!');
    }
  }
}
