import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { DietPlan, UserInfo } from 'src/entity/dietPlan';

const userInfoSchema = new Schema<UserInfo>(
  {
    id: {
      type: String,
    },
    age: {
      type: Number,
      default: null,
    },
    weight: {
      type: Number,
      default: null,
    },
    targetWeight: {
      type: Number,
      default: null,
    },
    weightType: {
      type: String,
      enum: ['lb', 'kg'],
      default: 'lb',
    },

    height: {
      type: Number,
      default: null,
    },
    heightType: {
      type: String,
      enum: ['ft', 'in','cm'],
      default: 'in',
    },

    gender: {
      type: String,
      enum: ['male', 'female'],
      default: 'male',
    },
    weeklyGoal: {
      type: String,
      default: '',
    },
    activityId: {
      type: String,
    },
  },
  { timestamps: false, versionKey: false, _id: false },
);

const dietPlanSchema = new Schema<DietPlan>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
    },
    user: {
      type: userInfoSchema,
      default: null,
    },
    type: {
      type: String,
      enum: ['maintain', 'loss', 'gain'],
      default: 'loss',
    },
    startDate: {
      type: Date,
      default: null,
    },
    endDate: {
      type: Date,
      default: null,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const dietPlanModel = model<DietPlan>('dietPlan', dietPlanSchema);
export { dietPlanModel };
