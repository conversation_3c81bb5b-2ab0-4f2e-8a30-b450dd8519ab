import { Test, TestingModule } from '@nestjs/testing';
import { ReminderService } from '../../../src/modules/reminder/services/reminder.service';
import { ReminderRepository } from '../../../src/modules/reminder/repositories/reminder.repository';
import { TopicManagementService } from '../../../src/modules/reminder/services/topic-management.service';
import { QueueInstance } from '../../../src/queue-system';
import { QueuePayloadType } from '../../../src/queue-system/predefined-data';

describe('Reminder Broadcast System', () => {
  let reminderService: ReminderService;
  let topicManagementService: TopicManagementService;
  let reminderRepository: ReminderRepository;
  let queueInstance: QueueInstance;

  const mockReminderRepository = {
    create: jest.fn(),
    findById: jest.fn(),
    findActiveRemindersForTime: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockQueueInstance = {
    sendPayload: jest.fn(),
  };

  const mockTopicManagementService = {
    subscribeToReminders: jest.fn(),
    unsubscribeFromReminders: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReminderService,
        {
          provide: ReminderRepository,
          useValue: mockReminderRepository,
        },
        {
          provide: TopicManagementService,
          useValue: mockTopicManagementService,
        },
        {
          provide: 'NotificationHelperService',
          useValue: {},
        },
        {
          provide: 'NotificationStatsService',
          useValue: {},
        },
        {
          provide: 'FcmService',
          useValue: {},
        },
      ],
    }).compile();

    reminderService = module.get<ReminderService>(ReminderService);
    topicManagementService = module.get<TopicManagementService>(TopicManagementService);
    reminderRepository = module.get<ReminderRepository>(ReminderRepository);
    
    // Mock QueueInstance singleton
    jest.spyOn(QueueInstance, 'getInstance').mockReturnValue(mockQueueInstance as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Reminder Creation and Scheduling', () => {
    it('should create reminder and schedule broadcast', async () => {
      const reminderData = {
        title: 'Daily Workout',
        message: 'Time for your daily workout!',
        time: '08:00',
        repeatDays: ['monday', 'wednesday', 'friday'],
      };

      const mockReminder = {
        id: 'reminder-123',
        ...reminderData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockReminderRepository.create.mockResolvedValue(mockReminder);

      const result = await reminderService.createReminder(reminderData);

      expect(result).toEqual(mockReminder);
      expect(mockReminderRepository.create).toHaveBeenCalledWith(reminderData);
      
      // Verify scheduling was called
      expect(mockQueueInstance.sendPayload).toHaveBeenCalled();
    });

    it('should send correct payload format for topic broadcasting', async () => {
      const reminderData = {
        title: 'Test Reminder',
        message: 'Test message',
        time: '12:00',
        repeatDays: ['monday'],
      };

      const mockReminder = { id: 'test-id', ...reminderData };
      mockReminderRepository.create.mockResolvedValue(mockReminder);

      await reminderService.createReminder(reminderData);

      const expectedPayload = expect.objectContaining({
        title: 'Test Reminder',
        body: 'Test message',
        topic: 'all-reminders',
        task: 'topic-sender',
        payloadType: QueuePayloadType.REMINDER_TOPIC_SENDER,
        isHighPriority: true,
      });

      expect(mockQueueInstance.sendPayload).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Buffer),
        expect.objectContaining({ delay: expect.any(Number) })
      );
    });
  });

  describe('Topic Management', () => {
    it('should subscribe users to reminders topic', async () => {
      const tokens = ['token1', 'token2', 'token3'];

      await topicManagementService.subscribeToReminders(tokens);

      expect(mockTopicManagementService.subscribeToReminders).toHaveBeenCalledWith(tokens);
    });

    it('should unsubscribe users from reminders topic', async () => {
      const tokens = ['token1', 'token2'];

      await topicManagementService.unsubscribeFromReminders(tokens);

      expect(mockTopicManagementService.unsubscribeFromReminders).toHaveBeenCalledWith(tokens);
    });
  });

  describe('Reminder Broadcasting', () => {
    it('should group reminders within 5-minute windows', async () => {
      const now = new Date();
      const reminders = [
        { id: '1', title: 'Reminder 1', message: 'Message 1', time: '08:00' },
        { id: '2', title: 'Reminder 2', message: 'Message 2', time: '08:03' },
        { id: '3', title: 'Reminder 3', message: 'Message 3', time: '08:07' },
      ];

      mockReminderRepository.findActiveRemindersForTime.mockResolvedValue(reminders);

      await reminderService.sendRemindersForTime(now);

      // Should create 2 groups: [1,2] and [3]
      expect(mockQueueInstance.sendPayload).toHaveBeenCalledTimes(2);
    });

    it('should handle empty reminder list gracefully', async () => {
      const now = new Date();
      mockReminderRepository.findActiveRemindersForTime.mockResolvedValue([]);

      await reminderService.sendRemindersForTime(now);

      expect(mockQueueInstance.sendPayload).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle queue sending errors gracefully', async () => {
      const reminderData = {
        title: 'Test',
        message: 'Test',
        time: '12:00',
        repeatDays: ['monday'],
      };

      mockReminderRepository.create.mockResolvedValue({ id: 'test', ...reminderData });
      mockQueueInstance.sendPayload.mockRejectedValue(new Error('Queue error'));

      // Should not throw error
      await expect(reminderService.createReminder(reminderData)).resolves.toBeDefined();
    });

    it('should handle repository errors', async () => {
      const reminderData = {
        title: 'Test',
        message: 'Test',
        time: '12:00',
        repeatDays: ['monday'],
      };

      mockReminderRepository.create.mockRejectedValue(new Error('Database error'));

      await expect(reminderService.createReminder(reminderData)).rejects.toThrow('Database error');
    });
  });
});
