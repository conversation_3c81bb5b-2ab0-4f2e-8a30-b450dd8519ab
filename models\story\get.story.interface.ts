export interface IGetStoryRes {
  id: string;
  userId: string;
  url: string;
  type: string;
  expireAt?: number;
  totalView?: number;
}

// story viewer list
export interface IStoryViewer {
  id: string;
  name: string;
}
export interface IStoryViewerList {
  viewerList: IStoryViewer[];
  storyId: string;
}

export interface IStoryViewerListSuccessRes {
  data: IStoryViewerList;
}

// fetch-stories response type
export interface IFetchStory {
  storyId: string;
  url: string;
  type: string;
  createdAt: Date;
  totalView?: number;
}

export interface IFetchStoryRes {
  stories: IFetchStory[];
  userId: string;
  username: string;
}
export interface IFetchStorySuccessRes {
  data: IFetchStoryRes[];
}

// Fit buddy id list
export interface IFitBuddyUserId {
  userId: string;
}
