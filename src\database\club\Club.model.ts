import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Club, Address, Location } from 'src/entity/club';

const AddressSchema = new Schema<Address>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      index: true,
    },
    addressLine1: String,
    addressLine2: String,
    postCode: String,
    city: String,
    state: String,
    country: String,
  },
  {
    _id: false,
    timestamps: false,
    versionKey: false,
  },
);

const PointSchema = new Schema<Location>(
  {
    type: {
      type: String,
      enum: ['Point'],
      required: true,
    },
    coordinates: {
      type: [Number],
      required: true,
    },
  },
  {
    _id: false,
  },
);

const ClubSchema = new Schema<Club>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    name: {
      type: String,
      required: true,
    },
    placeId: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    image: {
      cover: {
        type: String,
        required: true,
      },
      logo: {
        type: String,
        default: null,
      },
    },
    location: {
      type: PointSchema,
      required: true,
    },
    address: AddressSchema,
    description: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

ClubSchema.index({ location: '2dsphere' });
const ClubModel = model<Club>('club', ClubSchema);
export { ClubModel };
