import { SuccessResponse } from '../../common/index';
import { DietPlan } from './dietPlan';

/**
 * API Path: /api/diet/diet-plans
 * method: GET
 * response: GetAllDietPlansResponse
 */
export interface GetAllDietPlansQuery {
  offset?: number;
  limit?: number;
}
export interface GetAllDietPlansSuccessResponse extends SuccessResponse {
  data: DietPlan[];
}

export const enum GetAllDietPlansErrorMessages {
  CAN_NOT_GET_ALL_DIET_PLANS= 'Can not get all diet plans',
}
