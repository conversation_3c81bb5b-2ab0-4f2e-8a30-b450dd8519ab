import { Injectable } from '@nestjs/common';
import { PointRedemptionConfigModel } from '../common/db/point-redemption-config.model';
import { PointRedemptionConfigEntity } from '../common/entities/point-redemption-config.entity';

@Injectable()
export class PointRedemptionConfigRepository {
  async getActiveConfig(): Promise<PointRedemptionConfigEntity | null> {
    return await PointRedemptionConfigModel.findOne({ isActive: true }).lean();
  }

  async createOrUpdateConfig(
    conversionRate: number,
  ): Promise<PointRedemptionConfigEntity> {
    const updatedConfig = await PointRedemptionConfigModel.findOneAndUpdate(
      {},
      {
        $set: {
          conversionRate,
          isActive: true,
        },
      },
      {
        new: true,
        upsert: true,
        setDefaultsOnInsert: true,
      },
    ).lean();

    return updatedConfig;
  }

  async getAllConfigs(
    limit = 10,
    offset = 0,
  ): Promise<PointRedemptionConfigEntity[]> {
    return PointRedemptionConfigModel.find()
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();
  }
}
