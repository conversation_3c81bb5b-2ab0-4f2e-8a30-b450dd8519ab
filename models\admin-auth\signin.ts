import { SuccessResponse } from '../common/index';

/**
 * API Path: /admin-auth/sign-in
 * method: POST
 * body: SignInRequest
 * response: SignInSuccessResponse
 */

export interface SignInRequest {
  username: string;
  password: string;
}

export interface Token {
  token?: string;
}

export interface SignInSuccessResponse extends SuccessResponse {
  data: Token;
}

export const enum SignInErrorMessages {
  INVALID_CREDENTIALS = 'Invalid credentials',
  CAN_NOT_CREATE_SESSION = 'Can not create session',
}
