import { SuccessResponse } from '../common/index';
import { SpotProfile, SpotProfileImage } from './spotProfile';

/**
 * API Path: /spot/profile
 * method: PUT
 * body: SpotProfileBody
 * response: SpotProfileResponse
 */

export interface UpdateSpotProfileRequestBody {
  gender?: string;
  name?: string;
  
  profession?: {
    company?: string;
    position?: string;
  };
  bio?:string;
  workoutType?: string;
  bodyType?: string;
  interest?: string[];

  education?: {
    school?: string;
    college?: string;
    university?: string;
  };
  clubId?: string;
  maxBench?: number;
  maxSquat?: number;
  weightType?:string;
  ghostModeOn?: boolean;
  location?: {
    type: string;
    coordinates: number[];
  };
}

export interface UpdateSpotProfileSuccessResponse extends SuccessResponse {
  data: SpotProfile;
}

export const enum UpdateSpotProfileErrorMessages {
  PROFILE_NOT_FOUND = 'Profile not found',
  IMAGE_NOT_FOUND = 'Image not found',
}
