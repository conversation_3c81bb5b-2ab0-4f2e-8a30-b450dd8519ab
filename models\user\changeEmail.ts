import { SuccessResponse } from '../common/index';

/**
 * method: PATCH
 * body: UserChangeEmailRequest
 * response: UserChangeEmailResponse
 */

export interface UserChangeEmailRequest {
  email: string;
}

export interface UserChangeEmailMessage {
  message?: string;
}

export interface UserChangeEmailSuccessResponse extends SuccessResponse {
  data: UserChangeEmailMessage;
}

export const enum UserChangeEmailSuccessMessages {
  CHANGE_EMAIL_SUCCESSFUL = 'Email changed successfully',
}

export const enum UserChangeEmailErrorMessages {
  EMAIL_ALREADY_EXISTS = 'Email already exists',
  CAN_NOT_UPDATE_USER_NEW_EMAIL = 'Can not update user new email',
}
