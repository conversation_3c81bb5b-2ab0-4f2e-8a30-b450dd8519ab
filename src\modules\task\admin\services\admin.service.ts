import { Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { deepCasting } from 'src/internal/casting/object.casting';
import { throwNotFoundErr } from 'src/internal/exception/api.exception.ext';
import { TASK_NOT_FOUND } from '../../common/const/task.const';
import { TaskProviderForUser } from '../../user/providers/user.provider';
import {
  CreateTaskRequestDtoForAdmin,
  CreateTaskResponseDtoForAdmin,
  CreateTaskSuccessResponseDtoForAdmin,
} from '../dtos/admin-create-task.dto';
import {
  GetTaskResponseDtoForAdmin,
  GetTaskSuccessResponseDtoForAdmin,
  GetTasksQueryDtoForAdmin,
  GetTasksSuccessResponseDtoForAdmin,
} from '../dtos/admin-get-task.dto';
import {
  UpdateTaskRequestDtoForAdmin,
  UpdateTaskResponseDtoForAdmin,
  UpdateTaskSuccessResponseDtoForAdmin,
} from '../dtos/admin-update-task.dto';
import { TaskProviderForAdmin } from '../providers/admin.provider';

@Injectable()
export class TaskServiceForAdmin {
  constructor(
    private readonly taskProviderForAdmin: TaskProviderForAdmin,
    private readonly taskProviderForUser: TaskProviderForUser,
    private readonly helper: Helper,
  ) {}

  async createTask(
    taskData: CreateTaskRequestDtoForAdmin,
  ): Promise<CreateTaskSuccessResponseDtoForAdmin> {
    const task = await this.taskProviderForAdmin.createTask(taskData as any);

    const responseDto = deepCasting(CreateTaskResponseDtoForAdmin, task);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getTaskById(id: string): Promise<GetTaskSuccessResponseDtoForAdmin> {
    const task = await this.taskProviderForAdmin.getTaskById(id);
    throwNotFoundErr(!task, 'Task not found', TASK_NOT_FOUND);

    const responseDto = deepCasting(GetTaskResponseDtoForAdmin, task);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async updateTask(
    id: string,
    updateData: UpdateTaskRequestDtoForAdmin,
  ): Promise<UpdateTaskSuccessResponseDtoForAdmin> {
    const task = await this.taskProviderForAdmin.getTaskById(id);
    throwNotFoundErr(!task, 'Task not found', TASK_NOT_FOUND);

    const updatedTask = await this.taskProviderForAdmin.updateTask(
      id,
      updateData,
    );
    const responseDto = deepCasting(UpdateTaskResponseDtoForAdmin, updatedTask);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async deleteTask(id: string): Promise<any> {
    const task = await this.taskProviderForAdmin.getTaskById(id);
    throwNotFoundErr(!task, 'Task not found', TASK_NOT_FOUND);

    await this.taskProviderForAdmin.deleteTask(id);

    return this.helper.serviceResponse.successResponse([]);
  }

  async getAllTasks(
    query: GetTasksQueryDtoForAdmin,
  ): Promise<GetTasksSuccessResponseDtoForAdmin> {
    const tasks = await this.taskProviderForAdmin.getTasks({
      type: query.type,
      requirementType: query.requirementType,
      isActive: query.isActive,
    });

    const responseDtos = tasks.map((task) =>
      deepCasting(GetTaskResponseDtoForAdmin, task),
    );

    return this.helper.serviceResponse.successResponse(responseDtos);
  }
}
