import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { UserAuthController } from './rest';
import { UserAuthService } from './services';
import { authConfig } from 'config/auth';
import { UserRepository } from '../user/repositories';
import { JwtStrategy } from 'src/authentication/strategy/jwt-strategy';
import { SearchModule } from '../global-search/search.module';
import { UserAuthRepository } from './repositories';
import { UserAuthServiceV2 } from './services/user-auth.v2.service';
import { UserAuthControllerV2 } from './rest/user-auth.v2.controller';
import { FcmService } from '../../helper/fcmService';

@Module({
  imports: [
    JwtModule.register({
      secret: authConfig.jwt_key,
      signOptions: {
        expiresIn: authConfig.expiration_time,
      },
    }),
    SearchModule,
  ],
  controllers: [UserAuthController, UserAuthControllerV2],
  providers: [
    UserAuthService,
    UserAuthServiceV2,
    UserRepository,
    UserAuthRepository,
    JwtStrategy,
    FcmService,
  ],
  exports: [UserAuthRepository],
})
export class UserAuthModule {}
