import { PartialUserInfo } from 'models/user';
import { ReelsComment } from 'src/entity/reels';


export type CreateReelsCommentRequest = Omit<
  ReelsComment,
  'userId' | 'createdAt' | 'updatedAt' | 'reelsId'
>;

export type CreateReelsComment = Partial<ReelsComment>;

export type CreateReelsCommentResponseData = ReelsComment & {
  userInfo: PartialUserInfo;
};

export type CreateReelsCommentResponse = { data: CreateReelsCommentResponseData };
