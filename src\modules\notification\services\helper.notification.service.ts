import { Injectable } from '@nestjs/common';
import { NotificationCategory, NotificationPayload } from 'models';
import { QueueInstance } from 'src/queue-system';
import { QueueRelation } from 'src/queue-system/predefined-data/relation';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';
import { randomUUID } from 'crypto';
import { UserModel } from 'src/database/user/user.model';
import { sharedConfig } from 'config/shared';

@Injectable()
export class NotificationHelperService {
  constructor(private schedulerRegistry: SchedulerRegistry) {}

  async getFcmToken(userId) {
    const user = await UserModel.findOne({ id: userId })
      .select('fcmToken')
      .lean();
    return user?.fcmToken;
  }

  async getAllActiveUsers() {
    const users = await UserModel.find({
      fcmToken: { $exists: true, $ne: null },
      isActive: { $ne: false }, // Assuming there's an isActive field, adjust as needed
    })
      .select('id name fcmToken timezone')
      .lean();
    return users;
  }

  async sendNotifications(
    notificationPayload: NotificationPayload,
  ): Promise<void> {
    try {
      if (sharedConfig.processENV !== 'SERVER') {
        return;
      }

      // Notification Payload
      const {
        module,
        title,
        content,
        date,
        category,
        documentId,
        body,
        data,
        topic,
      } = notificationPayload;
      let payload = null;
      if (category === NotificationCategory.GROUP_CHAT) {
        payload = {
          title: title,
          body: body,
          data: data,
          topic: topic,
          payloadType: QueueRelation[category]?.generator?.type,
          module: module,
          content: content,
          category: category,
          task: 'generator',
          documentId: documentId,
          saveDatabase: false,
        };
      } else {
        const { recipient, createdBy } = notificationPayload;
        if (recipient?.id === createdBy?.id) {
          return;
        }
        const token = await this.getFcmToken(recipient.id);
        payload = {
          targetUsers: [recipient.id],
          createdBy: {
            name: createdBy.name,
            userId: createdBy.id,
            avatar: createdBy?.image?.profile,
          },
          fcmToken: token,
          payloadType: QueueRelation[category]?.generator?.type,
          module: module,
          title: title,
          content: content,
          category: category,
          task: 'generator',
          documentId: documentId,
          saveDatabase: true,
        };
      }

      // Send Notification queue
      async function generateQueue() {
        (await QueueInstance).sendPayload(
          QueueRelation[category]?.generator?.name,
          Buffer.from(JSON.stringify(payload)),
        );
      }
      generateQueue();

      // if(date){// schedule notification
      //   const job = new CronJob(`${date}`, () => {
      //     console.log('I am working')
      //     generateQueue();
      //   });

      //   this.schedulerRegistry.addCronJob(category+randomUUID(), job);
      //   job.start();

      // } else {// send notification immidiately
      //   generateQueue();
      // }
    } catch (error: any) {
      console.log(error.message);
    }
  }
}
// import { Injectable } from '@nestjs/common';
// import {
//   NotificationPayload,
// } from 'models';
// import { QueueInstance } from 'src/queue-system';
// import { QueueRelation } from 'src/queue-system/predefined-data/relation';
// import { SchedulerRegistry } from '@nestjs/schedule';
// import { CronJob } from 'cron';
// import { randomUUID } from 'crypto';
// const schedule = require('node-schedule');
// const moment = require('moment');

// @Injectable()
// export class NotificationHelperService {

//   constructor(private schedulerRegistry: SchedulerRegistry) {}

//   async sendNotifications(
//     notificationPayload: NotificationPayload,
//   ): Promise<void> {
//     try {
//           // Notification Payload
//           const { recipient, createdBy, module, title, content, date, category }=notificationPayload;
//           const payload = {
//             targetUsers: [recipient.id],
//             createdBy: {
//               name: createdBy.name,
//               userId: createdBy.id,
//               avatar: createdBy?.image?.profile,
//             },
//             fcmToken: recipient?.fcmToken,
//             payloadType: QueueRelation[category]?.generator?.type,
//             module: module,
//             title: title,
//             content: content,
//             category: category
//           };

//           // Send Notification queue

//           async function generateQueue(){
//             (await QueueInstance).sendPayload(
//               QueueRelation[category]?.generator?.name,
//               Buffer.from(JSON.stringify(payload)),
//             );
//             }

//             function getRules() {
//               const rule = new schedule.RecurrenceRule();
//               rule.year = moment(date).year();
//               rule.month = moment(date).month();
//               rule.date = moment(date).date();
//               rule.hour = moment(date).hours();
//               rule.minute = moment(date).minutes();
//               rule.second = moment(date).seconds();

//               return rule;
//             }

//             if(date){// schedule notification
//               const job = schedule.scheduleJob(getRules(), function(){
//                 generateQueue();
//               });

//             } else {// send notification immidiately
//               generateQueue();
//             }

//       } catch (error: any) {
//         console.log(error.message);
//       }
//     }
// }
