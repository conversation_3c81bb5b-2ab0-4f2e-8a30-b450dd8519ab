import { Injectable } from '@nestjs/common';
import { ReferralCodeEntity } from '../../common/entities/referral-code.entity';
import { ReferralCodeRepositoryForUser } from '../repositories/user.repository';

@Injectable()
export class ReferralsCodeProviderForUser {
  constructor(private readonly repository: ReferralCodeRepositoryForUser) {}

  async getSingleReferralCode(code: string): Promise<ReferralCodeEntity> {
    return await this.repository.findByCode(code);
  }

  async incrementUsage(code: string): Promise<void> {
    return this.repository.incrementUsage(code);
  }

  async incrementPendingReferrals(id: string): Promise<void> {
    return this.repository.incrementPendingReferrals(id);
  }

  async incrementSuccessFulReferrals(id: string): Promise<void> {
    return this.repository.incrementSuccessFulReferrals(id);
  }
}
