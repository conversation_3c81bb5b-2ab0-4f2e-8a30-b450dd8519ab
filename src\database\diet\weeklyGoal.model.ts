import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { WeeklyGoal } from 'src/entity/weeklyGoal';

const weeklyGoalSchema = new Schema<WeeklyGoal>(
  {
    id: {
        type:String,
        default: () => randomUUID(),
    },
    value: {
        type: String,
        default: null,
    },
},
{
  versionKey: false,
  timestamps: true,
},
);


const weeklyGoalModel = model<WeeklyGoal>(
  'weeklyGoal',
  weeklyGoalSchema,
);
export { weeklyGoalModel };
