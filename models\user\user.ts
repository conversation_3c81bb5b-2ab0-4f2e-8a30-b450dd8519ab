export const enum TagType {
  HOME = 'HOME',
  OFFICE = 'OFFICE',
  OTHERS = 'OTHERS',
}
export interface UserAddress {
  id?: string;
  firstName: string;
  lastName: string;
  addressLine1: string;
  addressLine2?: string;
  isDefault?: boolean;
  city: string;
  state?: string;
  country?: string;
  postCode?: string;
  phone: string;
  tag: TagType;
}

export interface User {
  id: string;
  name: string;
  phone?: string;
  email: string;
  addresses?: UserAddress[];
  fcmToken?: string;
  title?: string;
  organization?: string;
  workoutType?: string;
  bodyType?: string;
  dateOfBirth: Date;
  gender?: string;
  maxBench?: number;
  maxSquat?: number;
  weightType?: string;
  location?: {
    type: string;
    coordinates: number[];
  };
  image?: {
    profile: string;
  };
}

export type PartialUserInfo = {
  id: string;
  name: string;
  image: {
    profile: string;
  };
};
