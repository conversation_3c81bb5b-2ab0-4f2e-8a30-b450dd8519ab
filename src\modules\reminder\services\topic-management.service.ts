import { Injectable } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';

@Injectable()
export class TopicManagementService {
  constructor(private readonly fcmService: FcmService) {}

  /**
   * Subscribe user tokens to the all-reminders topic
   * This should be called when users register or login
   */
  async subscribeToReminders(tokens: string | string[]): Promise<void> {
    try {
      await this.fcmService.subscribeNotificationTopic(tokens, 'all-reminders');
      console.log(`Subscribed ${Array.isArray(tokens) ? tokens.length : 1} tokens to all-reminders topic`);
    } catch (error) {
      console.error('Failed to subscribe to reminders topic:', error.message);
    }
  }

  /**
   * Unsubscribe user tokens from the all-reminders topic
   * This should be called when users logout or delete account
   */
  async unsubscribeFromReminders(tokens: string | string[]): Promise<void> {
    try {
      await this.fcmService.unsubscribeNotificationTopic(tokens, 'all-reminders');
      console.log(`Unsubscribed ${Array.isArray(tokens) ? tokens.length : 1} tokens from all-reminders topic`);
    } catch (error) {
      console.error('Failed to unsubscribe from reminders topic:', error.message);
    }
  }

  /**
   * Bulk subscribe all active users to reminders topic
   * This can be used for initial setup or migration
   */
  async bulkSubscribeActiveUsers(): Promise<void> {
    try {
      // This would need to be implemented based on your user service
      // const activeUsers = await this.userService.getAllActiveUsersWithFcmTokens();
      // const tokens = activeUsers.map(user => user.fcmToken).filter(Boolean);
      // 
      // if (tokens.length > 0) {
      //   await this.subscribeToReminders(tokens);
      // }
      
      console.log('Bulk subscription to reminders topic completed');
    } catch (error) {
      console.error('Failed to bulk subscribe users to reminders topic:', error.message);
    }
  }
}
