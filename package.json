{"name": "backend", "version": "1.0.0", "description": "Fitsomnia Backend", "author": "Fitsomnia", "private": true, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "TZ=UTC+6 nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "TZ=UTC+6 nest start", "start:dev": "TZ=UTC+6 nest start --watch", "start:debug": "TZ=UTC+6 nest start --debug --watch", "start:prod": "TZ=UTC+6 node dist/main", "start:windows": "nest start --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --verbose --config ./test/jest-e2e.json", "test:unit": "jest --verbose --config ./test/jest-unit.json", "ibu": "yarn install && yarn build && yarn test:unit"}, "keywords": ["fitness", "e-commerce", "social"], "license": "ISC", "dependencies": {"@azure/communication-email": "^1.0.0", "@azure/storage-blob": "^12.26.0", "@easypost/api": "^6.1.0", "@elastic/elasticsearch": "^8.5.0", "@google-cloud/vertexai": "^1.9.0", "@nestjs-modules/ioredis": "^1.0.1", "@nestjs/common": "^8.4.4", "@nestjs/core": "^8.4.0", "@nestjs/elasticsearch": "^9.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^8.0.0", "@nestjs/passport": "^8.2.1", "@nestjs/platform-express": "^8.0.0", "@nestjs/platform-socket.io": "^9.2.1", "@nestjs/schedule": "^2.2.1", "@nestjs/swagger": "^5.2.1", "@nestjs/testing": "^8.4.7", "@nestjs/throttler": "^4.1.0", "@nestjs/websockets": "^8.4.7", "@nestjsi/class-validator": "^0.1.4", "@sendgrid/mail": "^7.7.0", "@types/cron": "^2.0.1", "@types/socket.io": "^3.0.2", "@willsoto/nestjs-prometheus": "^6.0.2", "amqplib": "^0.10.8", "app-store-server-api": "^0.9.0", "apple-signin-auth": "^1.7.4", "aws-sdk": "^2.1255.0", "axios": "^1.3.4", "bcrypt": "^5.0.1", "body-parser": "^1.20.2", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cookie-parser": "^1.4.6", "cron": "^2.3.0", "dotenv": "^16.0.0", "firebase-admin": "^11.3.0", "ioredis": "^5.2.4", "jest": "^28.1.1", "jsonwebtoken": "^9.0.0", "moment": "^2.29.4", "mongoose": "^6.2.2", "nestjs-throttler-storage-redis": "^0.3.3", "node-fetch": "2.6.7", "node-schedule": "^2.1.1", "nodemailer": "^6.7.5", "npm": "^11.2.0", "passport": "^0.5.2", "passport-jwt": "^4.0.0", "phone": "^3.1.37", "prom-client": "^15.1.3", "redis": "^4.5.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "socket.io": "^3.1.2", "stripe": "^11.14.0", "supertest": "^6.2.3", "swagger-ui-express": "^4.4.0", "ts-jest": "^28.0.0-next.3", "twilio": "^4.13.0"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/amqplib": "^0.10.0", "@types/bcrypt": "^5.0.0", "@types/cookie-parser": "^1.4.3", "@types/express": "^4.17.13", "@types/jest": "27.4.1", "@types/multer": "^1.4.7", "@types/node": "^16.0.0", "@types/node-fetch": "2.x", "@types/nodemailer": "^6.4.4", "@types/passport-jwt": "^3.0.6", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.2.5", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^27.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^4.3.5"}}