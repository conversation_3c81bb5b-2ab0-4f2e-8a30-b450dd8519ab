import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';

export interface IUserMealTracking {
  id: string;
  userId: string;
  date: Date; // Track which date these meals were added on
  breakfastAdded: boolean;
  lunchAdded: boolean;
  dinnerAdded: boolean;
  snacksAdded: boolean;
}

const UserMealTrackingSchema = new Schema<IUserMealTracking>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
    },
    date: {
      type: Date,
      required: true,
    },
    breakfastAdded: {
      type: Boolean,
      default: false,
    },
    lunchAdded: {
      type: Boolean,
      default: false,
    },
    dinnerAdded: {
      type: Boolean,
      default: false,
    },
    snacksAdded: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  },
);

// Create a compound index on userId and date to ensure uniqueness
UserMealTrackingSchema.index({ userId: 1, date: 1 }, { unique: true });

export const UserMealTrackingModel = model<IUserMealTracking>(
  'user-meal-tracking-entity',
  UserMealTrackingSchema,
);
