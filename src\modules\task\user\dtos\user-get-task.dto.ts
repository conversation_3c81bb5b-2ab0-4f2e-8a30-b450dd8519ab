import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { TaskType } from '../../common/entities/task.entity';
import { CompletionStatus } from '../../common/entities/user-task.entity';

export class GetUserTasksQueryDtoForUser {
  @ApiProperty({ enum: TaskType, required: false })
  @IsEnum(TaskType)
  @IsOptional()
  type?: TaskType;

  @ApiProperty({ 
    enum: ['active', 'completed'], 
    required: false,
    description: 'Filter tasks by completion status: "active" shows in-progress and not started tasks, "completed" shows completed tasks' 
  })
  @IsString()
  @IsOptional()
  status?: 'active' | 'completed';
}

export class UserTaskInfoDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  progress: number;

  @ApiProperty({ enum: CompletionStatus })
  completionStatus: CompletionStatus;

  @ApiProperty()
  pointsAwarded: boolean;
}

export class GetUserTaskResponseDtoForUser {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  description: string;

  @ApiProperty({ enum: TaskType })
  type: TaskType;

  @ApiProperty()
  targetValue: number;

  @ApiProperty()
  points: number;

  @ApiProperty()
  requirementType: string;

  @ApiProperty()
  deadline?: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  progress: any;

  @ApiProperty()
  completionStatus: CompletionStatus;

  @ApiProperty()
  pointsAwarded: boolean;

  // @ApiProperty({ type: UserTaskInfoDto })
  // userTask: {
  //   id: string;
  //   progress: number;
  //   completionStatus: CompletionStatus;
  //   pointsAwarded: boolean;
  // };
}

export class GetUserTaskSuccessResponseDtoForUser {
  @ApiProperty({ type: GetUserTaskResponseDtoForUser })
  data: GetUserTaskResponseDtoForUser;
}

export class GetUserTasksSuccessResponseDtoForUser {
  @ApiProperty({ type: [GetUserTaskResponseDtoForUser] })
  data: GetUserTaskResponseDtoForUser[];
}
