import { model, Schema } from 'mongoose';
import { AdvertisingProduct } from 'src/entity/product';

const AdvertisingProductSchema = new Schema<AdvertisingProduct>(
  {
    productId: {
      type: String,
      unique: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const AdvertisingProductModel = model<AdvertisingProduct>(
  'advertising-product',
  AdvertisingProductSchema,
);
export { AdvertisingProductModel };
