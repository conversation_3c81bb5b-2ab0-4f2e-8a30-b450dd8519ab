import { SuccessResponse } from '../common/index';


/**
 * API Path: /spot/unfriend
 * method: DELETE
 * body: UpdateSpotRequestBody
 * response: UnFriendSuccessResponse
 */

export const enum UnFriendFitBuddySuccessMessages {
    FITBUDDY_UNFRIEND_SUCCESSFULLY = "Fitbuddy unfriended successfully",
  }

export interface UnFriendSuccessResponse extends SuccessResponse {
    data: {
        message:UnFriendFitBuddySuccessMessages
    }
}


export const enum UnfriendErrorMessages {
    FITBUDDY_CAN_NOT_UNFRIEND='Can not unfriend fitbuddy'
  }
  