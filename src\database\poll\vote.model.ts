import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Vote } from 'src/entity/poll';



const VoteSchema = new Schema<Vote>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      trim: true,
    },
    pollId: {
        type: String,
        trim: true,
    },
    pollOptionId:{
        type:String,
        trim:true
    }
    
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const VoteModel = model<Vote>('vote', VoteSchema);
export { VoteModel };


