export enum IDayType {
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY',
}
export interface IIntermediateTraining {
  id?: string;
  name: string;
  description?: string;
  mechanics?: string;
  type?: string;
  category?: string[];
  forceType?: string[];
  primaryMuscles?: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview?: string;
  // Additional entity
  programId: string;
  week: number;
  day: IDayType;
  duration?: number;
}
