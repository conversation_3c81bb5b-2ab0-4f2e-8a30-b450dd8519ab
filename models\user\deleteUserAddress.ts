import { SuccessResponse } from '../common/index';
import { User } from './user';

/**
 * API Path: /user/delete-address/:addressId
 * method: DELETE
 * params: addressId
 * response: DeleteUserAddressResponse
 */

export interface DeleteUserAddressParams {
  addressId: string;
}

export interface DeleteUserAddressSuccessResponse extends SuccessResponse {
  data: User;
}

export const enum DeleteUserAddressErrorMessages {
  CAN_NOT_DELETE_USER_ADDRESS = 'Can not delete user address',
}

export interface IDeleteUserAccountReqByAdmin {
  userName: string;
  email: string;
}
