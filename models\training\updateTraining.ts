import { SuccessResponse } from 'models/common';
import { ITraining, ExpertiseLevel } from './training';
import { BaseExercise } from 'models';

/**
 * API Path: /api/training/{id}
 * method: PATCH
 * Param: UpdateTrainingParam
 * body: UpdateTrainingBody
 * response: UpdateTrainingResponse
 */

export class UpdateTrainingParam {
  id: string;
}

export class UpdateTrainingBody implements BaseExercise {
  name: string;
  description?: string;
  mechanics?: string;
  type: string;
  category: string[];
  forceType?: string[];
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview: string;
  duration: number;
  expertiseLevel: ExpertiseLevel;
}

export interface UpdateTrainingSuccessResponse extends SuccessResponse {
  data: ITraining;
}

export type UpdateTrainingResponse = UpdateTrainingSuccessResponse;
