export enum DayType {
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY',
}

export enum ExpertiseLevel {
  BEGINNER = 'BEGINNER',
  ADVANCE = 'ADVANCE',
}
export interface ITraining {
  id?: string;
  name: string;
  description?: string;
  mechanics?: string;
  type: string;
  category: string[];
  forceType?: string[];
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview: string;
  // Additional entity
  duration: number;
  expertiseLevel: ExpertiseLevel;
}
