import { SuccessResponse } from '../common/index';

/**
 * API Path: /admin/products/:productId
 * method: DELETE
 * params: productId
 * response: DeleteProductResponse
 */

export interface DeleteProductParams {
  productId: string;
}

export const enum DeleteProductSuccessMessage {
  PRODUCT_DELETED_SUCCESSFUL = 'Product deleted successfully',
}

export interface DeleteProductSuccessResponse extends SuccessResponse {
  data: {
    message?: DeleteProductSuccessMessage;
  };
}

export const enum DeleteProductErrorMessages {
  CAN_NOT_DELETE_PRODUCT = 'Can not delete product',
}
