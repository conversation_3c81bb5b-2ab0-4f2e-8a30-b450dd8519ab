import { Injectable } from '@nestjs/common';
import { IUpdatePointsList, IUserPoints } from 'models';
import { UserPointsRepository } from '../repositories';

@Injectable()
export class UserPointsProvider {
  constructor(private readonly repository: UserPointsRepository) {}

  async updatePoints(
    userId: string,
    data: IUpdatePointsList,
  ): Promise<IUserPoints> {
    return await this.repository.updatePoints(userId, data);
  }

  async getUserPointsById(userId: string): Promise<any> {
    return await this.repository.getPointsByUserId(userId);
  }
}
