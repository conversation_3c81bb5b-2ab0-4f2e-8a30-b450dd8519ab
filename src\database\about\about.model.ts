import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { About } from 'src/entity/about';

const AboutSchema = new Schema<About>(
  {
    id: {
      type: String,
      default: randomUUID(),
    },
    privacyPolicy: {
      type: String,
      default: null,
    },
    termsOfUse: {
      type: String,
      default: null,
    },
    aboutUs: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const AboutModel = model<About>('about', AboutSchema);
export { AboutModel };
