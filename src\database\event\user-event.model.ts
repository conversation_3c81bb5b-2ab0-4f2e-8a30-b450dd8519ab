import { randomUUID } from 'crypto';
import { model, Schema } from "mongoose";
import { JerseySizeEnum, SessionEnum, UserEventEntity } from "src/entity/event";

/**
 * This is the schema for UserEventEntity. By using this schema class we will create
 * the model class which is UserEventEntityModel.
 */
const UserEventEntitySchema = new Schema<UserEventEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true
    },
    eventId: {
      type: String,
      required: true
    },
    userId: {
      type: String,
      required: true,
    },
    registrationTime: {
      type: Date,
      default: new Date(),
      required: true
    },
    userName: {
      type: String,
      required: false
    },
    userMobile: {
      type: String,
      required: false
    },
    userEmail: {
      type: String,
      required: true
    },
    notificationEnabled: {
      type: Boolean,
      required: false
    },
    session: {
      type: String,
      enum: SessionEnum,
      required: true
    },
    bkashNumber: {
      type: String,
      required: true
    },
    trxId: {
      type: String,
      default: null,
      required: false
    },
    studentId: {
      type: String,
      required: true
    },
    jerseySize: {
      type: String,
      enum: JerseySizeEnum,
      required: true
    }
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

/**
 * This is the model object for UserEventEntity. This should be used to perform any operation
 * related to user events.
 */
const UserEventEntityModel = model<UserEventEntity>('user_event_entity', UserEventEntitySchema);
export { UserEventEntityModel };