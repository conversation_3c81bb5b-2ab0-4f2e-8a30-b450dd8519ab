import { SuccessResponse } from 'models/common';
import { ITraining, ExpertiseLevel } from './training';
import { BaseExercise } from 'models';

/**
 * API Path: /api/training
 * method: POST
 * body: CreateTrainingReq
 * response: CreateTrainingResponse
 */
export class CreateTrainingReq implements BaseExercise {
  id?: string;
  name: string;
  description?: string;
  mechanics?: string;
  type: string;
  category: string[];
  forceType?: string[];
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  equipments?: string[];
  preview: string;
  duration: number;
  expertiseLevel: ExpertiseLevel;
}

export class CreateTraining {
  baseExercise: BaseExercise;
  duration: number;
  expertiseLevel: ExpertiseLevel;
}
export class CreateTrainingRequest {
  trainingList: CreateTraining[];
}

export interface CreateTrainingSuccessResponse extends SuccessResponse {
  data: ITraining[];
}

export type CreateTrainingResponse = CreateTrainingSuccessResponse;
