import { SuccessResponse } from '../../common/index';

/**
 * API Path: /api/clubs/{clubId}/block-member/{id}
 * method: PATCH
 * params: BlockMemberParam
 * response: BlockMemberResponse
 */
export interface BlockMemberParam {
  id: string;
  clubId: string;
}

export const enum BlockMemberSuccessMessages {
  SUCCESSFULLY_CLUB_MEMBER_BLOCKED = 'Successfully club member blocked',
}

export const enum BlockMemberErrorMessages {
  NOT_A_MEMBER_OF_THIS_CLUB = 'Not a member of this club',
  CAN_NOT_BLOCK_CLUB_MEMBER = 'Can not block club member',
}

export interface BlockMemberSuccessResponse extends SuccessResponse {
  data: {
    message: BlockMemberSuccessMessages;
  };
}

export type BlockMemberResponse = BlockMemberSuccessResponse;
