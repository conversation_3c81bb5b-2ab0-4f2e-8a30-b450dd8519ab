import { SuccessResponse } from '../common/index';
import { Spotted } from './spot';

/**
 * API Path: /spot/request-update
 * method: PATCH
 * body: UpdateSpotRequestBody
 * response: UpdateSpotRequestResponse
 */

export interface UpdateSpotRequestBody {
  isAccepted: boolean;
}

export interface UpdateSpotRequestSuccessResponse extends SuccessResponse {
  data: Spotted;
}

export const enum UpdateSpotRequestErrorMessages {
  SPOT_REQUEST_NOT_FOUND = 'Spot request not found',
  SPOT_REQUEST_UPDATED_FAILED = 'Can not update spot request',
}
