/**
 * API Path: /api/my-clubs
 * method: GET
 * response: MyClubResponse
 */

import { SuccessResponse } from 'models/common';
import { Club } from '../club';

export interface MyClubSuccessResponse extends SuccessResponse {
  data: Club;
}

export const enum MyClubErrorMessages {
  CAN_NOT_GET_MY_CLUB = 'Can not get my club',
  YOU_ARE_NOT_A_MEMBER_OF_ANY_CLUB = 'You are not a member of any club',
}

export type MyClubResponse = MyClubSuccessResponse;
