// import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
// import { Document } from 'mongoose';
// import { v4 as uuidv4 } from 'uuid';

// export type PointRedemptionConfigDocument = PointRedemptionConfig & Document;

// @Schema({ timestamps: true })
// export class PointRedemptionConfig {
//   @Prop({ type: String, default: () => uuidv4() })
//   id: string;

//   @Prop({ type: Number, required: true })
//   conversionRate: number; // Amount in taka per point (e.g., 0.5, 1, 2)

//   @Prop({ type: Boolean, default: true })
//   isActive: boolean;
// }

// export const PointRedemptionConfigSchema = SchemaFactory.createForClass(PointRedemptionConfig);
