import { Injectable } from '@nestjs/common';
import { UserTaskEntityModel } from '../../common/db/user-task.model';
import { TaskType } from '../../common/entities/task.entity';
import {
  CompletionStatus,
  UserTaskEntity,
} from '../../common/entities/user-task.entity';

@Injectable()
export class TaskRepositoryForUser {
  async createUserTask(
    userId: string,
    taskId: string,
  ): Promise<UserTaskEntity> {
    const userTask = new UserTaskEntityModel({
      userId,
      taskId,
      progress: 0,
      completionStatus: CompletionStatus.NOT_STARTED,
      pointsAwarded: false,
    });

    const newUserTaskDoc = await userTask.save();
    return newUserTaskDoc.toObject();
  }

  async findUserTaskById(id: string): Promise<UserTaskEntity | null> {
    const userTask = await UserTaskEntityModel.findOne({ id }).exec();
    return userTask ? userTask.toObject() : null;
  }

  async findUserTaskByUserAndTaskId(
    userId: string,
    taskId: string,
  ): Promise<UserTaskEntity | null> {
    const userTask = await UserTaskEntityModel.findOne({
      userId,
      taskId,
    }).exec();
    return userTask ? userTask.toObject() : null;
  }

  async updateUserTaskProgress(
    id: string,
    progress: number,
    status?: CompletionStatus,
  ): Promise<UserTaskEntity | null> {
    const updateData: any = { progress };

    if (status) {
      updateData.completionStatus = status;
    }

    const updatedUserTask = await UserTaskEntityModel.findOneAndUpdate(
      { id },
      { $set: updateData },
      { new: true },
    ).exec();

    return updatedUserTask ? updatedUserTask.toObject() : null;
  }

  async markUserTaskCompleted(id: string): Promise<UserTaskEntity | null> {
    const updatedUserTask = await UserTaskEntityModel.findOneAndUpdate(
      { id },
      {
        $set: {
          completionStatus: CompletionStatus.COMPLETED,
          pointsAwarded: true,
          progress: 1.0,
        },
      },
      { new: true },
    ).exec();

    return updatedUserTask ? updatedUserTask.toObject() : null;
  }

  async findUserTasks(
    userId: string,
    options?: {
      type?: TaskType;
    },
  ): Promise<UserTaskEntity[]> {
    const userTasks = await UserTaskEntityModel.find({
      userId,
    }).exec();

    return userTasks.map((userTask) => userTask.toObject());
  }

  async findAllUserTasksByTaskId(taskId: string): Promise<UserTaskEntity[]> {
    const userTasks = await UserTaskEntityModel.find({ taskId }).exec();
    return userTasks.map((userTask) => userTask.toObject());
  }
}
