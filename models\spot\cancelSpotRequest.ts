import { SuccessResponse } from '../common/index';

/**
 * API Path: /spot/request/{id}
 * method: DELETE
 * response: CancelSpotRequestSuccessResponse
 */

export const enum CancelSpotRequestErrorMessages {
  CAN_NOT_CANCEL_REQUEST = 'Can not cancel request',
  SPOT_REQUEST_NOT_FOUND = 'Spot request not found'
}

export const enum CancelSpotRequestSuccessMessages {
  SPOT_REQUEST_CANCELLED_SUCCESSFULLY = 'Spot request cancelled successfully',
}

export interface CancelSpotRequestSuccessResponse extends SuccessResponse {
  data: {
    message?: CancelSpotRequestSuccessMessages;
  };
}
