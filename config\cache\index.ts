const {
  REDIS_CACHE_USERNAME,
  REDIS_CACHE_PASSWORD,
  REDIS_CACHE_HOST_NAME,
  REDIS_CACHE_HOST_PORT,
  REDIS_CACHE_EXPIRE_TIME,
  USER_FEED_CHANNEL,
  POST_CHANNEL,
  REELS_CHANNEL,
  BUDDY_OFFSET,
  FOLLOWEE_OFFSET,
  MAX_POST_DATE,
  MAX_POST_CACHE_LIMIT,
  POST_DATE_OFFSET,
  POST_IDS_LIMIT,
  POPULAR_POST_LIST_OFFSET,
  FOLLOWEE_POSTS_DEFAULT_OFFSET,
  ADVERTISING_PRODUCTS_DEFAULT_OFFSET,
  ADVERTISING_PRODUCTS_CHANNEL,
  BEST_SELLING_PRODUCTS_CHANNEL,
  TRENDING_PRODUCTS_CHANNEL,
  BEST_SELLING_PRODUCTS_DEFAULT_OFFSET,
  DEAL_PRODUCTS_CHANNEL,
  TRENDING_PRODUCTS_DEFAULT_OFFSET,
  THROTTLER_TTL,
  TH<PERSON><PERSON><PERSON>R_LIMIT,
  REELS_IDS_LIMIT
  
} = process.env;
const SEVERN_DAYS_IN_SEC = 7 * 24 * 60 * 60;

export const redisCacheConfig = {
  redis_url:
    REDIS_CACHE_USERNAME &&
    REDIS_CACHE_PASSWORD &&
    REDIS_CACHE_HOST_NAME &&
    REDIS_CACHE_HOST_PORT
      ? `rediss://${REDIS_CACHE_USERNAME}:${REDIS_CACHE_PASSWORD}@${REDIS_CACHE_HOST_NAME}:${REDIS_CACHE_HOST_PORT}`
      : 'redis://127.0.0.1:6379',
  expire_time: parseInt(REDIS_CACHE_EXPIRE_TIME) || SEVERN_DAYS_IN_SEC,
  user_feed_channel: USER_FEED_CHANNEL || 'feed/users',
  post_channel: POST_CHANNEL || 'posts',
  reels_channel: REELS_CHANNEL || 'reels',
  advertising_products_channel:
    ADVERTISING_PRODUCTS_CHANNEL || 'advertising/products',
  deal_products_channel: DEAL_PRODUCTS_CHANNEL || 'deal/products',
  buddy_default_offset: parseInt(BUDDY_OFFSET) || 10,
  followEe_default_offset: parseInt(FOLLOWEE_OFFSET) || 10,
  max_post_date: parseInt(MAX_POST_DATE) || 30,
  max_post_cache_limit: parseInt(MAX_POST_CACHE_LIMIT) || 1000,
  post_date_default_offset: parseInt(POST_DATE_OFFSET) || 2,
  postIds_default_limit: parseInt(POST_IDS_LIMIT) || 20,
  reelsIds_default_limit: parseInt(REELS_IDS_LIMIT) || 20,
  popular_posts_default_limit: parseInt(POPULAR_POST_LIST_OFFSET) || 50,
  popular_reels_default_limit: parseInt(POPULAR_POST_LIST_OFFSET) || 50,
  followEe_posts_default_limit: parseInt(FOLLOWEE_POSTS_DEFAULT_OFFSET) || 50,
  followEe_reels_default_limit: parseInt(FOLLOWEE_POSTS_DEFAULT_OFFSET) || 50,
  advertising_products_default_limit:
    parseInt(ADVERTISING_PRODUCTS_DEFAULT_OFFSET) || 20,
  best_selling_products_channel:
    BEST_SELLING_PRODUCTS_CHANNEL || 'best-selling/products',
  trending_products_channel: TRENDING_PRODUCTS_CHANNEL || 'trending/products',
  best_selling_products_default_limit:
    parseInt(BEST_SELLING_PRODUCTS_DEFAULT_OFFSET) || 10,
  trending_products_default_limit:
    parseInt(TRENDING_PRODUCTS_DEFAULT_OFFSET) || 10,

  throttler_ttl: parseInt(THROTTLER_TTL) || 60,
  throttler_limit: parseInt(THROTTLER_LIMIT) || 50,
};
