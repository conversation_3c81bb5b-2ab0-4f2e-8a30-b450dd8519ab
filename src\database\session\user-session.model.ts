import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { UserSession } from 'src/entity/user-session';

const UserSessionSchema = new Schema<UserSession>(
  {
    sessionId: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      index: true,
    },
    expireAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const UserSessionModel = model<UserSession>('user-session', UserSessionSchema);
export { UserSessionModel };
