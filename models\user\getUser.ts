import { ProfileVisibilityEnum } from 'models/preference';
import { RelationStatusEnum } from 'models/spot';
import { SuccessResponse } from '../common/index';
import { User } from './user';

/**
 * API Path: /user
 * method: GET
 * response: GetUserInformationResponse
 */

export interface UserOtherInformation {
  buddyCount: number;
  followingCount: number;
  relationStatus?: RelationStatusEnum;
  profileVisibility?: ProfileVisibilityEnum;
}

export interface GetUserInformationSuccessResponse extends SuccessResponse {
  data: Omit<User, 'fcmToken'> & UserOtherInformation;
}

export const enum GetUserInformationErrorMessages {
  USER_NOT_FOUND = 'User not found',
  CAN_NOT_SEE_THE_USER_INFO = 'Can not see the user information',
}
