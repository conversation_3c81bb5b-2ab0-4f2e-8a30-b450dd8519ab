import { forwardRef, Module } from '@nestjs/common';
import { UserPointsModule } from '../user-points/user-points.module';
import { TaskControllerForAdmin } from './admin/controllers/admin.controller';
import { TaskProviderForAdmin } from './admin/providers/admin.provider';
import { TaskRepositoryForAdmin } from './admin/repositories/admin.repository';
import { TaskServiceForAdmin } from './admin/services/admin.service';
import { TaskProviderForInternal } from './providers/internal.provider';
import { TaskProgressService } from './task-progress.service';
import { TaskControllerForUser } from './user/controllers/user.controller';
import { TaskProviderForUser } from './user/providers/user.provider';
import { TaskRepositoryForUser } from './user/repositories/user.repository';
import { TaskServiceForUser } from './user/services/user.service';

@Module({
  imports: [forwardRef(() => UserPointsModule)],
  controllers: [TaskControllerForAdmin, TaskControllerForUser],
  providers: [
    TaskServiceForAdmin,
    TaskServiceForUser,
    TaskProgressService,
    TaskRepositoryForAdmin,
    TaskRepositoryForUser,
    TaskProviderForAdmin,
    TaskProviderForUser,
    TaskProviderForInternal,
  ],
  exports: [TaskProgressService],
})
export class TaskModule {}
