import { Injectable } from '@nestjs/common';
import { MealType } from 'models';
import { PostCommentModel } from 'src/database/post/postComment.model';
import { TaskPostReactionModel } from 'src/database/post/postTaskReactions.model';

import { UserMealTrackingModel } from './common/db/user-meal-tracking.model';
import { TaskRequirementType } from './common/entities/task.entity';
import { TaskProviderForInternal } from './providers/internal.provider';

@Injectable()
export class TaskProgressService {
  constructor(
    private readonly taskProviderForInternal: TaskProviderForInternal,
  ) {}

  async trackPostCreation(userId: string): Promise<void> {
    await this.taskProviderForInternal.trackUserActivity(
      userId,
      TaskRequirementType.POST_CREATION,
      1,
    );
  }

  async trackPostReaction(userId: string, postId: string): Promise<void> {
    // Check if this user-post reaction pair already exists
    const existingTracking = await TaskPostReactionModel.findOne({
      userId,
      postId,
    }).lean();

    // Only track and award progress if this is the first time the user is reacting to this post
    if (!existingTracking) {
      // Create a record to track this reaction
      await TaskPostReactionModel.create({
        userId,
        postId,
      });

      // Award task progress only for unique post reactions
      await this.taskProviderForInternal.trackUserActivity(
        userId,
        TaskRequirementType.POST_REACTION,
        1,
      );
    }
  }

  async trackProfileCompletion(userId: string): Promise<void> {
    await this.taskProviderForInternal.trackUserActivity(
      userId,
      TaskRequirementType.PROFILE_COMPLETION,
      1,
    );
  }

  async trackFriendInvitation(userId: string, count = 1): Promise<void> {
    await this.taskProviderForInternal.trackUserActivity(
      userId,
      TaskRequirementType.FRIEND_INVITATION,
      count,
    );
  }

  async trackCoachProfile(userId: string): Promise<void> {
    await this.taskProviderForInternal.trackUserActivity(
      userId,
      TaskRequirementType.COACH_PROFILE,
      1,
    );
  }

  async trackPostComment(userId: string, postId: string): Promise<void> {
    // Check if this user has already commented on this specific post
    const existingComment = await PostCommentModel.findOne({
      userId,
      postId,
    }).lean();

    // Only track and award progress if this is the first time the user comments on this post
    if (!existingComment) {
      await this.taskProviderForInternal.trackUserActivity(
        userId,
        TaskRequirementType.POST_COMMENT,
        1,
      );
    }
  }

  async trackDietCreation(userId: string): Promise<void> {
    await this.taskProviderForInternal.trackUserActivity(
      userId,
      TaskRequirementType.DIET_CREATION,
      1,
    );
  }

  async trackMealAdded(
    userId: string,
    mealType: MealType,
    date: Date,
  ): Promise<void> {
    // Normalize the date to start of day (remove time component)
    const trackingDate = new Date(date);
    trackingDate.setHours(0, 0, 0, 0);

    // Find or create tracking record for this user and date
    let mealTracking = await UserMealTrackingModel.findOne({
      userId,
      date: trackingDate,
    });

    if (!mealTracking) {
      mealTracking = await UserMealTrackingModel.create({
        userId,
        date: trackingDate,
        breakfastAdded: false,
        lunchAdded: false,
        dinnerAdded: false,
        snacksAdded: false,
      });
    }

    // Check if this meal type is already marked as added for this day
    let isNewMealType = false;

    switch (mealType) {
      case MealType.BREAKFAST:
        if (!mealTracking.breakfastAdded) {
          mealTracking.breakfastAdded = true;
          isNewMealType = true;
        }
        break;
      case MealType.LUNCH:
        if (!mealTracking.lunchAdded) {
          mealTracking.lunchAdded = true;
          isNewMealType = true;
        }
        break;
      case MealType.DINNER:
        if (!mealTracking.dinnerAdded) {
          mealTracking.dinnerAdded = true;
          isNewMealType = true;
        }
        break;
      case MealType.SNACKS:
        if (!mealTracking.snacksAdded) {
          mealTracking.snacksAdded = true;
          isNewMealType = true;
        }
        break;
    }
    // If this is a new meal type for today, save the tracking record and increment progress
    if (isNewMealType) {
      await mealTracking.save();

      // Each meal type contributes 0.25 (25%) to the daily progress
      const progress = 0.25;

      // Call the existing method to update task progress
      await this.taskProviderForInternal.trackUserActivity(
        userId,
        TaskRequirementType.MEAL_TRACKING,
        progress,
      );
    }
  }

  async trackWaterConsumption(userId: string): Promise<void> {
    await this.taskProviderForInternal.trackUserActivity(
      userId,
      TaskRequirementType.WATER_CONSUMPTION,
      1,
    );
  }

  // New method for tracking Spot profile creation
  async trackSpotProfileCreation(userId: string): Promise<void> {
    await this.taskProviderForInternal.trackUserActivity(
      userId,
      TaskRequirementType.SPOT_PROFILE_CREATION,
      1,
    );
  }

  // Add to TaskProgressService
  async trackSpotRequest(userId: string): Promise<void> {
    await this.taskProviderForInternal.trackUserActivity(
      userId,
      TaskRequirementType.SPOT_REQUEST,
      1, // Increment by 1 for each spot request
    );
  }
}
