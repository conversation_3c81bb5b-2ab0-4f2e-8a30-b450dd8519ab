import { SuccessResponse } from '../common/index';
import { Manufacturer } from './manufacturer';
import { ManufacturerSeo } from './manufacturerSeo';

/**
 * API Path: /manufacturers/{manufacturerId}
 * method: PATCH
 * body: UpdateManufacturerRequest
 * response: UpdateManufacturerResponse
 */

export const enum UpdateManufacturerErrorMessages {
  MANUFACTURER_NOT_FOUND = 'Manufacturer not found',
  THE_SAME_NAME_MANUFACTURER_ALREADY_EXISTS = 'The same name manuafacturer already exists',
  MANUFACTURER_NOT_UPDATED = 'Manufacturer not updated',
}

export const enum UpdateManufacturerSuccessMessages {
  MANUFACTURER_UPDATED_SUCCESSFULLY = 'Manufacturer updated successfully',
}

export interface UpdateManufacturerRequest {
  name?: string;
  description?: string;
  picture?: string;
  isPublished?: boolean;
  displayOrder?: number;
  seo?: ManufacturerSeo;
}

export interface UpdateManufacturerSuccessResponse extends SuccessResponse {
  data: {
    manufacturer: Manufacturer;
    message: UpdateManufacturerSuccessMessages;
  };
}
