import { Poll } from 'src/entity/poll';
import { SuccessResponse } from '../common/index';


/**
 * API Path: /tags/:tagId
 * method: GET
 * params: tagId
 * response: GetTagResponse
 */

export interface GetPollParams {
  pollId: string;
}

export interface GetPollSuccessResponse extends SuccessResponse {
  data: Poll;
}

export const enum GetPollErrorMessages {
  CAN_NOT_GET_POLL = 'Can not get poll',
}
