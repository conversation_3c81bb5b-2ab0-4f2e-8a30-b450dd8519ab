import { model, Schema } from 'mongoose';
import { Product } from 'src/entity/product';
import { randomUUID } from 'crypto';

const ProductSchema = new Schema<Product>(
  {
    id: {
      type: String,
      unique: true,
      default: () => randomUUID(),
    },
    info: {
      name: {
        type: String,
        required: true,
      },
      shortDescription: {
        type: String,
        default: null,
      },
      fullDescription: {
        type: String,
        default: null,
      },
      sku: String,
      price: Number,
      oldPrice: Number,
      showOnHomePage: {
        type: Boolean,
        default: false,
      },
      includeInTopMenu: {
        type: Boolean,
        default: false,
      },
      allowToSelectPageSize: {
        type: Boolean,
        default: false,
      },
      published: {
        type: Boolean,
        default: false,
      },
      displayOrder: {
        type: Number,
        default: 1,
      },
      isFeatured: {
        type: Boolean,
        default: false,
      },
      publishDate: {
        type: Date,
        default: new Date(),
      },
      size: { type: [String], default: [] },
      color: { type: [String], default: [] },
      stock: { type: Number, default: null },
      length: { type: Number, default: null },
      height: { type: Number, default: null },
      width: { type: Number, default: null },
      weight: { type: Number, default: null },
      weightUnit: { type: String, default: '' },
    },
    meta: {
      keywords: {
        type: [String],
        default: [],
      },
      title: {
        type: String,
        default: null,
      },
      description: {
        type: String,
        default: null,
      },
      friendlyPageName: String,
    },
    tags: [String],
    photos: {
      type: [
        {
          url: String,
          id: {
            type: String,
            index: true,
            default: () => randomUUID(),
          },
          title: {
            type: String,
            default: null,
          },
          color: {
            type: String,
            default: null,
          },
          alt: {
            type: String,
            default: null,
          },
          displayOrder: {
            type: Number,
            default: 1,
          },
        },
      ],
      default: [],
      _id: false,
    },
    brands: [String],
    manufacturer: {
      id: String,
      name: String,
    },
    categories: [
      {
        id: String,
        name: String,
        _id: false,
      },
    ],
    avgRating: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

ProductSchema.index(
  {
    'info.name': 'text',
    'info.fullDescription': 'text',
  },
  { name: 'search' },
);

const ProductModel = model<Product>('product', ProductSchema);

export { ProductModel };
