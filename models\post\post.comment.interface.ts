import { PartialUserInfo } from 'models/user';
import { PostComment } from 'src/entity/post';

export type CreatePostCommentRequest = Omit<
  PostComment,
  'userId' | 'createdAt' | 'updatedAt' | 'postId'
>;

export type CreatePostComment = Partial<PostComment>;

export type CreatePostCommentResponseData = PostComment & {
  userInfo: PartialUserInfo;
};

export type CreatePostCommentResponse = { data: CreatePostCommentResponseData };
