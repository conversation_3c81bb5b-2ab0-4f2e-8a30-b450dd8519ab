import { Body, Controller, Get, HttpStatus, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { CreateRedemptionPointConversionRequestDto, CreateRedemptionPointConversionSuccessResponseDto } from '../dtos/create-redemption.dto';
import { GetRedemptionPointResponseDtoSuccessResponseDto } from '../dtos/get-redemption.dto';
import { PointRedemptionService } from '../services/point-redemption.service';

@ApiTags('Point Redemption API - Admin')
@ApiBearerAuth()
@UseGuards(new RolesGuard(['admin']))
@Controller('admin/point-redemption')
export class AdminPointRedemptionController {
  constructor(private readonly pointRedemptionService: PointRedemptionService) {}

  @ApiOperation({ summary: 'Set conversion rate for point redemption' })
  @ApiBody({
      description: 'Create a Redemption point for conversion',
      type: CreateRedemptionPointConversionRequestDto
  })
  @ApiResponse({
      description: 'Get Active conversion rate response',
      type: CreateRedemptionPointConversionSuccessResponseDto,
      status: HttpStatus.CREATED,
    })
  @Post('config')
  async setConversionRate(@Body() body: CreateRedemptionPointConversionRequestDto) {
    return  this.pointRedemptionService.createOrUpdateConfig(body.conversionRate);
  }

  @ApiOperation({ summary: 'Get active conversion rate configuration' })
  @Get('config/active')
  @ApiResponse({
      description: 'Get Active conversion rate response',
      type: GetRedemptionPointResponseDtoSuccessResponseDto,
      status: HttpStatus.OK,
  })
  async getActiveConfig() {
    return await this.pointRedemptionService.getActiveConfig();
  }

}
