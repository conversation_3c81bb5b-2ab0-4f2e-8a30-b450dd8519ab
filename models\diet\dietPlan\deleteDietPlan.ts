import { SuccessResponse } from '../../common/successResponse';

export interface DeleteDietPlanRequest {
  dietPlanId: string;
}

export const enum DietMessage {
  REMOVE_DIET_PLAN_SUCCESSFULLY = 'Removed DietPlan successfully',
}

export interface DeleteDietMessage {
  message: DietMessage;
}

export interface DeleteDietPlanSuccessResponse extends SuccessResponse {
  data: DeleteDietMessage;
}

export const enum DeleteDietPlanErrorMessage {
  CAN_NOT_REMOVE_DIET_PLAN = 'Can not remove DietPlan',
}
