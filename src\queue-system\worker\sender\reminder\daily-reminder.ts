import { Injectable, Module, OnApplicationBootstrap } from '@nestjs/common';
import { FcmService } from 'src/helper/fcmService';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName } from 'src/queue-system/predefined-data';
import { FCMSenderPayload } from 'src/queue-system/types';

class FcmServiceHelper extends FcmService {}

@Injectable()
export class DailyReminderSenderQueue implements OnApplicationBootstrap {
  async onApplicationBootstrap() {
    try {
      await (
        await QueueInstance
      ).consume(NotificationQueueName.DAILY_REMINDER_SENDER_QUEUE);
    } catch (error) {
      console.log(error.message);
    }
  }

  static async sender(payload: FCMSenderPayload): Promise<void> {
    try {
      const { title, body, token, tokens, isHighPriority, data, documentId, task } = payload;

      // Handle bulk sending for better performance
      if (task === 'bulk-sender' && tokens && tokens.length > 0) {
        // Use FCM sendToMany for bulk notifications
        await new FcmServiceHelper().sendToMany(
          tokens,
          title,
          body,
          documentId,
          data,
          isHighPriority,
          600, // ttl
        );
      } else if (token && body) {
        // Individual notification
        await new FcmServiceHelper().sendToIndividual({
          token,
          title,
          body,
          documentId,
          data,
          isHighPriority,
          ttl: 600,
        });
      }
    } catch (error: any) {
      // Removed error logging for performance as requested
    }
  }
}

@Module({
  providers: [DailyReminderSenderQueue],
})
export class DailyReminderSenderModule {}
