import { model, Schema } from 'mongoose';
import { ReelsComment } from 'src/entity/reels';
// import { PostComment } from 'src/entity/post';

const ReelsCommentSchema = new Schema<ReelsComment>(
  {
    userId: {
      type: String,
      index: true,
      required: true,
    },
    reelsId: {
      type: String,
      index: true,
      required: true,
    },
    comment: {
      type: String,
      trim: true,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const ReelsCommentModel = model<ReelsComment>('reels-comment', ReelsCommentSchema);
export { ReelsCommentModel };
