export enum IChatContentTypeEnum {
  TEXT = 'text',
  IMAGE = 'image',
}
export class ICreateOneToOneChat {
  id: string;
  senderId: string;
  receiverId: string;
  type: string;
  content: string;
  seenAt?: string;
  createdAtu?: string;
  updatedAt?: string;
}

export class ICreateOneToOneChatReq {
  id: string
  type: IChatContentTypeEnum;
  content: string;
}

export class IOneToOneChatRes extends ICreateOneToOneChat {}

export class IOneToOneCreateChatRes {
  success: boolean;
  message?: string;
}

export class IDeleteMessageRes {
  message: string;
}

export interface ILastMessageInfo {
  id: string;
  senderId: string;
  receiverId: string;
  type: string;
  content: string;
  isLastSeen: boolean;
  createdAt: string;
}

export interface IOneToOneConversationListRes {
  lastMessageInfo: ILastMessageInfo;
  fitBuddyId: string;
  name: string;
  image: string;
  isOnline: boolean;
}

export interface IImage {
  profile: string;
}

export interface IActiveFitBuddyInfo {
  id: string;
  name: string;
  image: IImage;
}
export interface IActiveFitBuddy {
  userInfo: IActiveFitBuddyInfo;
}
