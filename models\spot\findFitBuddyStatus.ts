import { SuccessResponse } from '../common/index';
import {
  FitBuddiesStatus,
  FitBuddiesType,
  RelationStatusEnum,
} from './findFitBuddies';

/**
 * API Path: /spot/fitBuddy-status
 * method: GET
 * Query: FitBuddyStatusAndTypeQuery
 * response: FitBuddyStatusAndTypeSuccessResponse
 */

export interface FitBuddyStatusAndTypeQuery {
  fitBuddyId: string;
}

export interface FitBuddyStatusAndType {
  status: FitBuddiesStatus;
  type: FitBuddiesType;
  relationStatus?: RelationStatusEnum;
}

export const enum FitBuddyStatusAndTypeErrorMessages {
  CAN_NOT_GET_FITBUDDY_STATUS_AND_TYPE = 'Can not get fitBuddy status and type',
}

export interface FitBuddyStatusAndTypeSuccessResponse extends SuccessResponse {
  data: FitBuddyStatusAndType;
}
