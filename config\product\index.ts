const {
  BEST_SELLING_PRODUCTS_DEFAULT_DAYS,
  BEST_SELLING_PRODUCTS_DEFAULT_ORDERED_TOTAL_QUANTITY,
  TRENDING_PRODUCTS_DEFAULT_DAYS,
  TRENDING_PRODUCTS_DEFAULT_ORDERED_TOTAL_QUANTITY,
  DEAL_PRODUCTS_EXPIRATION_TIME,
} = process.env;
const SEVEN_DAYS_IN_MILLISEC = 7 * 60 * 60 * 24 * 1000;
const ONE_DAYS_IN_MILLISEC = 1 * 60 * 60 * 24 * 1000;
const ONE_DAY_IN_SEC = 1 * 60 * 60 * 24;

export const productConfig = {
  trendingProducts: {
    defaultDays:
      parseInt(TRENDING_PRODUCTS_DEFAULT_DAYS) || ONE_DAYS_IN_MILLISEC,
    orderedTotalQuantity:
      parseInt(TRENDING_PRODUCTS_DEFAULT_ORDERED_TOTAL_QUANTITY) || 10,
  },
  bestSellingProducts: {
    defaultDays:
      parseInt(BEST_SELLING_PRODUCTS_DEFAULT_DAYS) || SEVEN_DAYS_IN_MILLISEC,
    orderedTotalQuantity:
      parseInt(BEST_SELLING_PRODUCTS_DEFAULT_ORDERED_TOTAL_QUANTITY) || 5,
  },
  dealProducts: {
    expireTimeInSec: parseInt(DEAL_PRODUCTS_EXPIRATION_TIME) || ONE_DAY_IN_SEC,
  },
};
