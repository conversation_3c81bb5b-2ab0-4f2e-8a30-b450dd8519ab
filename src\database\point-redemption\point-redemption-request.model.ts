// import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
// import { Document } from 'mongoose';
// import { v4 as uuidv4 } from 'uuid';

// export enum PointRedemptionStatus {
//   PENDING = 'pending',
//   APPROVED = 'approved',
//   REJECTED = 'rejected',
//   COMPLETED = 'completed'
// }

// export type PointRedemptionRequestDocument = PointRedemptionRequest & Document;

// @Schema({ timestamps: true })
// export class PointRedemptionRequest {
//   @Prop({ type: String, default: () => uuidv4() })
//   id: string;

//   @Prop({ type: String, required: true })
//   userId: string;

//   @Prop({ type: Number, required: true })
//   pointsRedeemed: number;

//   @Prop({ type: Number, required: true })
//   conversionRate: number; // The conversion rate at the time of request

//   @Prop({ type: Number, required: true })
//   amountInTaka: number; // Total amount to be paid (pointsRedeemed * conversionRate)

//   @Prop({ 
//     type: String, 
//     enum: Object.values(PointRedemptionStatus),
//     default: PointRedemptionStatus.PENDING 
//   })
//   status: PointRedemptionStatus;

//   @Prop({ type: String, required: false })
//   adminNote: string;

//   @Prop({ type: String, required: false })
//   paymentMethod: string;

//   @Prop({ type: String, required: false })
//   paymentDetails: string;

//   @Prop({ type: Date, required: false })
//   processedAt: Date;
// }

// export const PointRedemptionRequestSchema = SchemaFactory.createForClass(PointRedemptionRequest);
