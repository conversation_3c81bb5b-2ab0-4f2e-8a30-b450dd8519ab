import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Spotted } from 'src/entity/spot';

const SpotRequestSchema = new Schema<Spotted>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    requesterId: {
      type: String,
      required: true,
    },
    recipientId: {
      type: String,
      required: true,
    },
    isAccepted: Boolean,
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

SpotRequestSchema.index({ requesterId: 1, recipientId: 1, isAccepted: 1 });
const SpotRequestModel = model<Spotted>('Spot-Request', SpotRequestSchema);
export { SpotRequestModel };
