import { SuccessResponse } from "models/common";
import { Poll } from "./poll";

export interface votePollRequestBody{
    pollOptionId?: string;
    
}



export interface VotePollSuccessResponse extends SuccessResponse{
    data: Poll
}



export type VotePollResponse =VotePollSuccessResponse;


export const enum VotePollErrorMessages {
    INVALID_POLL_ID= 'Invalid Poll Id',
    USER_NOT_FOUND = 'User Not Found',
    TEAM_NOT_FOUND = 'Team Not Found',
    PLAYER_NOT_FOUND = 'Player Not Found',
    PLAYER_ALREADY_EXIST = 'Player Already Exist',
    PLAYER_DOES_NOT_EXIST = 'Player Does Not Exist',
    TITLE_DOES_NOT_EXIST = 'Title Does Not Exist',
    POLL_EXPIRED = 'Poll Expired',
    POLL_OPTION_ID = 'Poll Option is not Exist'
  }