import { forwardRef, <PERSON>du<PERSON> } from "@nestjs/common";
import { UserModule } from "src/modules/user/user.rest.module";
import { CampaignModule } from "../campaign/campaign.module";
import { ReferralCampaignControllerForUser } from "./controllers/user.controller";
import { ReferralsCodeProviderForUser } from "./providers/user.provider";
import { ReferralCodeRepositoryForUser } from "./repositories/user.repository";
import { ReferralCodeServiceForUser } from "./services/user.service";

@Module({
    controllers: [
        ReferralCampaignControllerForUser
      ],

    imports: [
      forwardRef(() => UserModule),
      forwardRef(() => CampaignModule)
    ],
    providers: [
              ReferralCodeServiceForUser,
              ReferralCodeRepositoryForUser,
              ReferralsCodeProviderForUser],
    exports: [ReferralsCodeProviderForUser],
    
})
export class ReferralCodeModule{ }