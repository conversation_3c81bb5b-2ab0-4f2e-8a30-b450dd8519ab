import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import {
  TaskRequirementType,
  TaskType,
} from '../../common/entities/task.entity';

export class CreateTaskRequestDtoForAdmin {
  @ApiProperty({ description: 'Task title' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Task description' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ enum: TaskType, default: TaskType.DAILY })
  @IsEnum(TaskType)
  type: TaskType;

  @ApiProperty({ enum: TaskRequirementType })
  @IsEnum(TaskRequirementType)
  requirementType: TaskRequirementType;

  @ApiProperty({ description: 'Target value to complete the task' })
  @IsInt()
  @Min(1)
  targetValue: number;

  @ApiProperty({ description: 'Points rewarded on completion' })
  @IsInt()
  @Min(1)
  points: number;

  @ApiProperty({ description: 'Task deadline', required: false })
  @IsOptional()
  // @IsDate()
  deadline?: Date;
}

export class CreateTaskResponseDtoForAdmin {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  title: string;

  @Expose()
  @ApiProperty()
  description: string;

  @Expose()
  @ApiProperty({ enum: TaskType })
  type: TaskType;

  @Expose()
  @ApiProperty({ enum: TaskRequirementType })
  requirementType: TaskRequirementType;

  @Expose()
  @ApiProperty()
  targetValue: number;

  @Expose()
  @ApiProperty()
  points: number;

  @Expose()
  @ApiProperty()
  isActive: boolean;

  @Expose()
  @ApiProperty({ required: false })
  deadline?: Date;
}

export class CreateTaskSuccessResponseDtoForAdmin {
  @ApiProperty({ type: CreateTaskResponseDtoForAdmin })
  data: CreateTaskResponseDtoForAdmin;
}
