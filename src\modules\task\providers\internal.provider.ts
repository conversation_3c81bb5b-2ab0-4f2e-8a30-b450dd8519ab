import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { IUserPointsSources, IUserPointsTransTypeEnum } from 'models';
import { UserPointsProvider } from 'src/modules/user-points/providers/user-points.provider';
import { TaskProviderForAdmin } from '../admin/providers/admin.provider';
import { TaskRequirementType } from '../common/entities/task.entity';
import { CompletionStatus } from '../common/entities/user-task.entity';
import { TaskProviderForUser } from '../user/providers/user.provider';

@Injectable()
export class TaskProviderForInternal {
  constructor(
    @Inject(forwardRef(() => TaskProviderForUser))
    private readonly taskProviderForUser: TaskProviderForUser,
    @Inject(forwardRef(() => TaskProviderForAdmin))
    private readonly taskProviderForAdmin: TaskProviderForAdmin,
    @Inject(forwardRef(() => UserPointsProvider))
    private readonly userPointsProvider: UserPointsProvider,
  ) {}

  async trackUserActivity(
    userId: string,
    requirementType: TaskRequirementType,
    increment = 1,
  ): Promise<void> {
    // Get all active tasks that match this requirement type
    const tasks = await this.taskProviderForAdmin.getTasks({
      requirementType,
      isActive: true,
    });

    const now = new Date();

    // For each task, update the user's progress
    for (const task of tasks) {
      // Check if the task deadline has expired
      if (task.deadline && new Date(task.deadline) < now) {
        console.log(`Task ${task.id} has expired`);
        continue;
      }
      let userTask = await this.taskProviderForUser.getUserTaskByUserAndTaskId(
        userId,
        task.id,
      );

      // If the user doesn't have this task yet, create it
      if (!userTask) {
        userTask = await this.taskProviderForUser.createUserTask(
          userId,
          task.id,
        );
      }

      // Skip if task is already completed
      if (userTask.completionStatus === CompletionStatus.COMPLETED) {
        continue;
      }

      // Update progress
      // const newProgress = userTask.progress + increment;

      // // Check if task is now completed
      // if (newProgress >= task.targetValue) {
      //   // Use the dedicated method for completed tasks
      //   await this.taskProviderForUser.markUserTaskCompleted(userTask.id);

      //   // Award points

      //   await this.userPointsProvider.updatePoints(userId, {
      //     type: IUserPointsTransTypeEnum.EARNED,
      //     pointSourceType: IUserPointsSources.DailyTasks,
      //     pointSourceId: task.id,
      //     pointSourceName: task.title,
      //     points: task.points,
      //   });
      // } else if (newProgress > 0) {
      //   // For in-progress tasks, update with the appropriate status
      //   await this.taskProviderForUser.updateUserTaskProgress(
      //     userTask.id,
      //     newProgress,
      //     CompletionStatus.IN_PROGRESS,
      //   );
      // }

      const newProgressCount = userTask.progress * task.targetValue + increment;
      const normalizedProgress =
        Math.round((newProgressCount / task.targetValue) * 100) / 100;

      console.log(
        `Task ${task.id}: Updating progress to ${newProgressCount}/${task.targetValue} = ${normalizedProgress}`,
      );

      // Check if task is now completed - use a threshold of 0.98 instead of exactly 1
      // This handles floating-point precision issues
      if (normalizedProgress >= 0.98) {
        console.log(
          `Task ${task.id} is complete (progress: ${normalizedProgress})`,
        );

        // Set progress to 1.0 when marking as completed
        await this.taskProviderForUser.updateUserTaskProgress(
          userTask.id,
          1.0, // Always set to exactly 1.0 for completed tasks
          CompletionStatus.COMPLETED,
        );

        // Award points
        await this.userPointsProvider.updatePoints(userId, {
          type: IUserPointsTransTypeEnum.EARNED,
          pointSourceType: IUserPointsSources.DailyTasks,
          pointSourceId: task.id,
          pointSourceName: task.title,
          points: task.points,
        });
      } else {
        // For in-progress tasks, update with the appropriate status
        await this.taskProviderForUser.updateUserTaskProgress(
          userTask.id,
          normalizedProgress,
          CompletionStatus.IN_PROGRESS,
        );
      }
    }
  }
}
