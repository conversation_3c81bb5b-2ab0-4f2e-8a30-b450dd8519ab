import {
    Body,
    Controller,
    HttpStatus,
    Post,
    UseGuards
} from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiBody,
    ApiOperation,
    ApiResponse,
    ApiTags
} from '@nestjs/swagger';
import {
    OnlyAuthGuard,
    OnlyRoleGuard,
    Role,
    Roles,
} from 'src/authentication/guards/auth-role.guard';
import { REFERRAL_API_FOR_USER } from '../../common/const/swagger.const';

import { ReferralSuccessResponseDto, UseReferralCodeRequestDto } from '../dtos/create-referrals.dto';
import { ReferralsServiceForUser } from '../services/user.service';


@ApiTags(REFERRAL_API_FOR_USER)
@ApiBearerAuth()
@Controller('user/referral')
export class ReferralsControllerForUser { 
    constructor(private service: ReferralsServiceForUser) { }
    
    @Post('join')
    @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
    @Roles(Role.User)
    @ApiOperation({ summary: 'Join with referral code' })
    @ApiBody({ type: UseReferralCodeRequestDto })
    @ApiResponse({
        description: 'Returns the referrals value',
        type: ReferralSuccessResponseDto,
        status: HttpStatus.CREATED,
    })
    async createReferral( @Body() body: { referralCode: string; refereeUserId: string }) {
        return await this.service.validateAndUseReferralCode(body);
     }
}