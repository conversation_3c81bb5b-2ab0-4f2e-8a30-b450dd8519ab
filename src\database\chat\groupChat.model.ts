import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { GroupChat } from 'src/entity/groupChat';

const GroupChatSchema = new Schema<GroupChat>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    senderId: {
      type: String,
    },
    groupId: {
      type: String,
    },
    type: {
      type: String,
      enum: ['text', 'image'],
      default: 'text',
    },
    content: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

GroupChatSchema.index({ senderId: 1, groupId: 1 });
const GroupChatModel = model<GroupChat>('group-chat', GroupChatSchema);
export { GroupChatModel };
