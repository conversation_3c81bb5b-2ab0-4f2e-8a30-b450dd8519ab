import { SuccessResponse } from '../common/index';

/**
 * API Path: /api/spot/not/:userId
 * method: PUT
 * response: SpotNotResponse
 */

export const enum SpotNotSuccessMessages {
  SPOT_NOT_SUCCESSFULLY = 'spot notted successfully',
}

export const enum SpotNotErrorMessages {
  CAN_NOT_SPOT_NOT = 'Can not spot not user',
  SPOT_PROFILE_NOT_EXIST = 'spot profile with this userId doesn\'t exist',
  LOGGED_IN_SPOT_PROFILE_NOT_EXIST = 'spot profile of loggedin userId doesn\'t exist'
}

export interface SpotNotSuccessResponse extends SuccessResponse {
  data: {
    message?: SpotNotSuccessMessages;
  };
}
