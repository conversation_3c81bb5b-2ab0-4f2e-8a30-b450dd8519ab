import { Injectable } from '@nestjs/common';
import { TaskEntityModel } from '../../common/db/task.model';
import {
  TaskEntity,
  TaskRequirementType,
  TaskType,
} from '../../common/entities/task.entity';

@Injectable()
export class TaskRepositoryForAdmin {
  async createTask(taskData: TaskEntity): Promise<TaskEntity> {
    const newTask = new TaskEntityModel(taskData);
    const newTaskDoc = await newTask.save();
    return newTaskDoc.toObject();
  }

  async findTaskById(id: string): Promise<TaskEntity | null> {
    const task = await TaskEntityModel.findOne({ id }).exec();
    return task ? task.toObject() : null;
  }

  async updateTask(
    id: string,
    updateData: Partial<TaskEntity>,
  ): Promise<TaskEntity | null> {
    const updatedTask = await TaskEntityModel.findOneAndUpdate(
      { id },
      { $set: updateData },
      { new: true },
    ).exec();

    return updatedTask ? updatedTask.toObject() : null;
  }

  async deleteTask(id: string): Promise<boolean> {
    const result = await TaskEntityModel.deleteOne({ id }).exec();
    return result.deletedCount > 0;
  }

  async findTasks(options?: {
    type?: TaskType;
    requirementType?: TaskRequirementType;
    isActive?: boolean;
  }): Promise<TaskEntity[]> {
    const query: any = {};

    if (options) {
      if (options.type) {
        query.type = options.type;
      }

      if (options.requirementType) {
        query.requirementType = options.requirementType;
      }

      if (options.isActive !== undefined) {
        query.isActive = options.isActive;
      }
    }

    const tasks = await TaskEntityModel.find(query).select('-_id').exec();
    return tasks.map((task) => task.toObject());
  }
}
