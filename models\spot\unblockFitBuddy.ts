import { SuccessResponse } from '../common/index';

/**
 * API Path: /spot/unblock-fitBuddy/{id}
 * method: PATCH
 * response: UnblockFitBuddySuccessResponse
 */

export const enum UnblockFitBuddyErrorMessages {
  CAN_NOT_UNBLOCK_FITBUDDY = 'Can not unblock fitBuddy',
  FITBUDDY_NOT_FOUND = 'FitBuddy not found',
  FITBUDDY_ALREADY_UNBLOCKED = 'FitBuddy already unblocked',
}

export const enum UnblockFitBuddySuccessMessages {
  FITBUDDY_UNBLOCKED_SUCCESSFULLY = 'FitBuddy unblocked successfully',
}

export interface UnblockFitBuddySuccessResponse extends SuccessResponse {
  data: {
    message?: UnblockFitBuddySuccessMessages;
  };
}
