import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import {
  TaskRequirementType,
  TaskType,
} from '../../common/entities/task.entity';

export class UpdateTaskRequestDtoForAdmin {
  @ApiProperty({ description: 'Task title', required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: 'Task description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ enum: TaskType, required: false })
  @IsEnum(TaskType)
  @IsOptional()
  type?: TaskType;

  @ApiProperty({ enum: TaskRequirementType, required: false })
  @IsEnum(TaskRequirementType)
  @IsOptional()
  requirementType?: TaskRequirementType;

  @ApiProperty({
    description: 'Target value to complete the task',
    required: false,
  })
  @IsInt()
  @Min(1)
  @IsOptional()
  targetValue?: number;

  @ApiProperty({
    description: 'Points rewarded on completion',
    required: false,
  })
  @IsInt()
  @Min(1)
  @IsOptional()
  points?: number;

  @ApiProperty({ description: 'Task active status', required: false })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({ description: 'Task deadline', required: false })
  @IsOptional()
  @IsDate()
  deadline?: Date;
}

export class UpdateTaskResponseDtoForAdmin {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  title: string;

  @Expose()
  @ApiProperty()
  description: string;

  @Expose()
  @ApiProperty({ enum: TaskType })
  type: TaskType;

  @Expose()
  @ApiProperty({ enum: TaskRequirementType })
  requirementType: TaskRequirementType;

  @Expose()
  @ApiProperty()
  targetValue: number;

  @Expose()
  @ApiProperty()
  points: number;

  @Expose()
  @ApiProperty()
  isActive: boolean;

  @Expose()
  @ApiProperty({ required: false })
  deadline?: Date;
}

export class UpdateTaskSuccessResponseDtoForAdmin {
  @ApiProperty({ type: UpdateTaskResponseDtoForAdmin })
  data: UpdateTaskResponseDtoForAdmin;
}
