import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { ConsumedFood, DietHistory, FoodSource, MealType } from 'src/entity/dietHistory';

const consumedFoodSchema = new Schema<ConsumedFood>(
  {
    id: {
        type:String,
        default: () => randomUUID(),
        unique: true,
    },
    foodId: {
        type:String,
        default: null,
    },
    name: {
        type: String,
        default: null,
    },
    servingSize: {
        type: Number,
        default: 1,
    },
    calories: {
        type: Number,
        default: null,
    },
    carb: {
        type: Number,
        default: null,
    },
    fat: {
        type: Number,
        default: null,
    },
    protein: {
        type: Number,
        default: null,
    },
    source:{
        type: String,
        enum: FoodSource,
        default: FoodSource.GLOBAL,
    },
    mealType: {
        type: String,
        enum: MealType,
        default: MealType.BREAKFAST,
    },
},
{
 _id: false,
  versionKey: false,
  timestamps: true,
},
);

const dietHistorySchema = new Schema<DietHistory>(
    {
        id: {
            type:String,
            default: () => randomUUID(),
            unique: true,
        },
        userId: {
            type: String,
        },
        date:{
        type: Date,
        default: null,
        },
        consumedFoods: {
        type: [consumedFoodSchema],
        default: null,
        },
        totalCalorieConsumed: {
            type: Number,
            default: 0,
        },
        waterConsumption: {
            type: Number,
            default: 0,
        }
  },
  {
   // _id: false,
    versionKey: false,
    timestamps: true,
  },
);


const dietHistoryModel = model<DietHistory>(
    'diet-history',
    dietHistorySchema,
  );
  export { dietHistoryModel };
  
  
