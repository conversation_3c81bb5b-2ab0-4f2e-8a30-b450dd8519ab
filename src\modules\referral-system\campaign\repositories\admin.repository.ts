import { Injectable } from "@nestjs/common";
import { ReferralCampaignModel } from "../../common/db/referral-campaign.model";
import { ReferralCampaignEntity } from "../../common/entities/referral-campaign.entity";
import { CampaignStatusForAdmin } from "../dtos/get-referral-campaign.dto";

@Injectable()
export class ReferralCampaignRepositoryForAdmin{


   async createCampaign(campaign: Partial<ReferralCampaignEntity>): Promise<ReferralCampaignEntity> {
       
          const info = new ReferralCampaignModel(campaign);
          const campaignDocument = await info.save();
          return campaignDocument.toObject();
      }

  async getReferralCampaigns(adminCampaignStatus: string, offset: number, limit: number): Promise<ReferralCampaignEntity[]> {
    const searchQuery: any = { isDeleted: false };
     if (adminCampaignStatus && adminCampaignStatus !== CampaignStatusForAdmin.ANY) {
         searchQuery.status = adminCampaignStatus;
        }
    
      const campaignDoc = await ReferralCampaignModel.find(searchQuery).skip(offset).limit(limit).exec();
      return campaignDoc.map(item => item.toObject());
  }

  async getReferralCampaign(campaignId: String): Promise<ReferralCampaignEntity | null> {
    const searchQuery = { id: campaignId, isDeleted: false };
    const referralCampaignDoc = await ReferralCampaignModel.findOne(searchQuery).exec();
    return referralCampaignDoc !== null ? referralCampaignDoc.toObject() : null;
  }

  async updateReferralCampaign(campaignId: string, info: ReferralCampaignEntity): Promise<ReferralCampaignEntity> {
      const updatedDoc = await ReferralCampaignModel.findOneAndUpdate(
        { id: campaignId, isDeleted: false },
        { $set: info },
        { new: true, runValidators: true }
      ).exec();
      return updatedDoc !== null ? updatedDoc.toObject() : null;
    }

}