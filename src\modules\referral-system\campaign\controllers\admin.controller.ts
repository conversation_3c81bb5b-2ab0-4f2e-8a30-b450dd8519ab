import { Body, Controller, Get, HttpStatus, Param, ParseUUI<PERSON><PERSON><PERSON>, Patch, Post, Query, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from "@nestjs/swagger";
import { OnlyAuthGuard, OnlyRoleGuard, Role, Roles } from "src/authentication/guards/auth-role.guard";
import { DEFAULT_PAGINATION_LIMIT } from "../../common/const/referral.pagination.const";
import { REFERRAL_API_FOR_ADMIN } from "../../common/const/swagger.const";
import { CampaignResponseDto, CreateCampaignDto, ReferralCampaignSuccessResponseDto } from "../dtos/create-referral-campaign.dto";
import { getMultipleReferralCampaignSuccessResponseDto, GetReferralCampaignQueryDtoForAdmin, getSingleReferralCampaignSuccessResponseDto } from "../dtos/get-referral-campaign.dto";
import { UpdateReferralCampaignRequestDto } from "../dtos/update-referral-campaign.dto";
import { ReferralCampaignServiceForAdmin } from "../services/admin.service";


@ApiTags(REFERRAL_API_FOR_ADMIN)
@ApiBearerAuth()
@Controller('admin/referral')

export class ReferralCampaignControllerForAdmin{
  constructor(private service: ReferralCampaignServiceForAdmin) { }
    @Post('campaign')
    @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
    @Roles(Role.Admin)
    @ApiOperation({ summary: 'Create a new referral campaign' })
    @ApiBody({ type: CreateCampaignDto })
    @ApiResponse({
        status: 201,
        description: 'Campaign created successfully',
        type: CampaignResponseDto
    })
    async createCampaign(@Body() dto: CreateCampaignDto): Promise<any> {
        return await this.service.createReferralCampaign(dto);
    }
      
    @Get('campaigns')
    @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
    @Roles(Role.Admin)
    @ApiOperation({ summary: 'Get multiple campaigns' })
   @ApiQuery({
      name: 'offset',
      description: 'How many initial pending profiles you want to skip',
      type: Number,
      required: false
      })
    @ApiQuery({
      name: 'limit',
      description: 'How many pending profile objects you want',
      type: Number,
      required: false
    })
    @ApiResponse({
      description: 'Get multiple campaign available',
      type: getMultipleReferralCampaignSuccessResponseDto,
      status: HttpStatus.OK,
    })
    async getMultipleReferralCampaigns(@Query() queryDto: GetReferralCampaignQueryDtoForAdmin) {
      const offset = queryDto.offset ?? 0;
      const limit = queryDto.limit ?? DEFAULT_PAGINATION_LIMIT;
      const adminCampaignStatus = queryDto.adminCampaignStatus ?? null;
      return await this.service.getMultipleReferralCampaign(adminCampaignStatus,offset,limit);
    }
    
    @Get('campaign/:campaignId')
    @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
    @Roles(Role.Admin)
    @ApiOperation({ summary: 'Get single Campaign' })
    @ApiParam({
        name: 'campaignId',
        description: 'The campaignId whose data you want',
        type: String
    })
    @ApiResponse({
      description: 'Get single campaign',
      type: getSingleReferralCampaignSuccessResponseDto,
      status: HttpStatus.OK,
    })
    async getSingleReferralCampaign(@Param('campaignId', ParseUUIDPipe) campaignId: string) {
      return await this.service.getSingleReferralCampaign(campaignId);
  }
  

  @Patch('campaign/:campaignId')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.Admin)
  @ApiOperation({ summary: 'Update single campaign' })
  @ApiParam({
      name: 'campaignId',
      description: 'The campaignId whose data you want to update',
      type: String
  })
  @ApiBody({
    description: 'Update a campaign',
    type: UpdateReferralCampaignRequestDto
  })
  @ApiResponse({
    description: 'Returns the updated campaign data',
    type: ReferralCampaignSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async updateSingleReferralCampaign(
    @Param('campaignId', ParseUUIDPipe) campaignId: string,
    @Body() updateDto: UpdateReferralCampaignRequestDto
  ) {
     return await this.service.updateSingleReferralCampaign(campaignId, updateDto);
    }
    

}