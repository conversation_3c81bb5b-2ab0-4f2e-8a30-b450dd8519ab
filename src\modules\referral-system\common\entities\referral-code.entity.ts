
export enum ReferralCodeStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    EXPIRED = 'EXPIRED',
    USAGE_LIMIT_REACHED = 'USAGE_LIMIT_REACHED'
}

export class ReferralCodeEntity {
    id?: string;
    code?: string;               // The actual referral code
    userId?: string;             // User who owns this code
    campaignId?: string;         // Campaign this code belongs to
    
    // Usage Tracking
    usageCount?: number;         // Number of times used
    usageLimit?: number;        // Max number of times it can be used
    lastUsedAt?: Date;          // Last time the code was used
    
    // Validity
    isActive?: boolean;          // Whether code is currently active
    status?: ReferralCodeStatus; // Current status of the code
    createdAt?: Date;           // When code was created
    expiresAt?: Date;           // When code expires
    
    // Stats
    totalPointsGenerated?: number;  // Total points generated from this code
    successfulReferrals?: number;   // Number of successful referrals
    pendingReferrals?: number;      // Number of pending referrals
}