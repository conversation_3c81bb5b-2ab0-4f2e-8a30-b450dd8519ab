// referral-participation.repository.ts

import { Injectable } from '@nestjs/common';
import { ReferralParticipationModel } from '../../common/db/referral-participation.model';


@Injectable()
export class ReferralParticipationRepository {
  async hasUserJoinedCampaign(userId: string, campaignId: string) {
    return ReferralParticipationModel.findOne({ userId, campaignId }).lean();
  }

  async createParticipation(userId: string, campaignId: string, referralCodeId: string) {
    return ReferralParticipationModel.create({
      userId,
      campaignId,
      referralCodeId,
    });
  }
}
