import { IChatContentTypeEnum } from './oneToOneChat.interface';

export class ICreateGroupChat {
  id: string;
  senderId: string;
  groupId: string;
  type: string;
  content: string;
  createdAt?: string;
  updatedAt?: string;
}

export class ICreateGroupChatReq {
  id: string;
  type: IChatContentTypeEnum;
  content: string;
}

export class IGroupChatRes extends ICreateGroupChat {}

export class IGroupCreateChatRes {
  success: boolean;
  message?: string;
}
export interface IGroupConversationListRes {
  name: string;
  image: string;
  groupId: string;
}
