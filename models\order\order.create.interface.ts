import { BaseOrderModel } from './order.response.interface';

export interface CreateProductOrderDetails {
  productId: string;
  name: string;
  price: number;
  quantity: number;
  sku: string;
  size: string;
  color?: string;
  parcel?: {
    length?: number;
    width?: number;
    height?: number;
    weight: number;
  };
}

export interface CreateOrderRequest extends BaseOrderModel {
  products: CreateProductOrderDetails[];
}

export const enum OrderModuleErrorMessages {
  ERROR_IN_PAYMENT_SESSION = 'Error in initializing payment session',
  PAYMENT_METHOD_IS_INVALID = 'Payment method is invaild',
  ERROR_IN_ORDER_CREATION = 'Error in order creation',
  INVALID_ITEM_SELECTED = 'Invalid item(s) selected or stock out',
  MISCALCULATION_OF_INDIVIDUAL_PRODUCT_PRICE = 'Miscalculation of individual product calculated prices',
}
