import { productConfig } from 'config/product';
import { model, Schema } from 'mongoose';
import { DealProduct } from 'src/entity/product';

const DealProductSchema = new Schema<DealProduct>(
  {
    productId: {
      type: String,
      unique: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

DealProductSchema.index(
  { createdAt: 1 },
  { expireAfterSeconds: productConfig.dealProducts.expireTimeInSec },
);
const DealProductModel = model<DealProduct>('deal-product', DealProductSchema);
export { DealProductModel };
