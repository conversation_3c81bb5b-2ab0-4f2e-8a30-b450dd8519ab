const {
  FIREBASE_ACCOUNT_TYPE,
  FIREBASE_PROJECT_ID,
  FIREBASE_PRIVATE_KEY_ID,
  FIREBASE_PRIVATE_KEY,
  FIREBASE_CLIENT_EMAIL,
  FIREBASE_CLIENT_ID,
  FIREBASE_AUTH_URL,
  FIREBASE_TOKEN_URI,
  FIREBASE_AUTH_PROVIDER_X509_CERT_URL,
  FIREBASE_CLIENT_X509_CERT_URL,
} = process.env;
export const fireBaseConfig = {
  type: FIREBASE_ACCOUNT_TYPE || '',
  project_id: FIREBASE_PROJECT_ID || '',
  private_key_id: FIREBASE_PRIVATE_KEY_ID || '',
  private_key: FIREBASE_PRIVATE_KEY
    ? FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n')
    : '',
  client_email: FIREBASE_CLIENT_EMAIL || '',
  client_id: FIREBASE_CLIENT_ID || '',
  auth_uri: FIREBASE_AUTH_URL || '',
  token_uri: FIREBASE_TOKEN_URI || '',
  auth_provider_x509_cert_url: FIREBASE_AUTH_PROVIDER_X509_CERT_URL || '',
  client_x509_cert_url: FIREBASE_CLIENT_X509_CERT_URL || '',

};
