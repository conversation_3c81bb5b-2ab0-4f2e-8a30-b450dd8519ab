import { SuccessResponse } from '../common/successResponse';
import { Cart } from './cartCommon/cart';

export interface AddToCartRequest {
  productId: string;
  quantity: number;
  size: string;
  color?: string;
}

export interface AddToCartSuccessResponse extends SuccessResponse {
  data: Cart;
}

export const enum AddToCartErrorMessage {
  CAN_NOT_CREATE_CART = 'Can not create cart',
  CAN_NOT_ADD_ITEM_TO_THE_CART = 'Can not add item to the cart',
  CAN_NOT_INCREMENT_CART_ITEM = 'Can not increment cart item',
}
