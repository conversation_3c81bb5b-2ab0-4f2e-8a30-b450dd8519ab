import { SuccessResponse } from '../common/index';
import { Product } from './product';

export interface GetCustomizedProductsQuery {
  offset?: number;
  limit?: number;
  categoryId: string;
  tag: string;
}

export interface GetCustomizedProductsSuccessResponse extends SuccessResponse {
  data: Product[];
}

export const enum GetCustomizedProductsErrorMessages {
  CAN_NOT_GET_CUSTOMIZED_PRODUCTS = 'Can not get customized products',
  INVALID_TAG_NAME = 'Invalid tag name',
}

export enum GetCustomizedProductsTagsEnum {
  BEST_SELLING_PRODUCTS = 'BEST_SELLING_PRODUCTS',
  TRENDING_PRODUCTS = 'TRENDING_PRODUCTS',
  TOP_RATED_PRODUCTS = 'TOP_RATED_PRODUCTS',
  DEAL_OF_THE_DAY = 'DEAL_OF_THE_DAY',
  YOU_MAY_LIKE = 'YOU_MAY_LIKE',
}
