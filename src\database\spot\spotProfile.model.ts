import { randomUUID } from 'crypto';
import { SpotProfileGenderEnum } from 'models';
import { model, Schema } from 'mongoose';
import { SpotProfile } from 'src/entity/spotProfile';
import { SpotProfileImage } from 'src/entity/spotProfileImage';

const spotProfileImageSchema = new Schema<SpotProfileImage>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
    },
    url: {
      type: String,
      default: null,
    },
  },
  {
    _id: false,
    versionKey: false,
    timestamps: true,
  },
);

const spotProfileSchema = new Schema<SpotProfile>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
    },
    userId: {
      type: String,
    },
    name: {
      type: String,
    },
    bio: {
      type: String,
      maxlength: 150,
      default:null,
    },
    
    education: {
      type: {
        school: { type: String, default: null },
        college: { type: String, default: null },
        university: { type: String, default: null },
      },
      default: null,
    },
    

    profession: {
      type: {
        company: { type: String, default: null },
        position: { type: String, default: null },
      },
      default: null,
    },
    age: {
      type: Number,
      default: null,
    },
    gender: {
      type: String,
      enum: ['male', 'female', 'prefer not say'],
      default: SpotProfileGenderEnum.MALE,
    },
    workoutType: {
      type: String,
      default: null,
    },
    bodyType: {
      type: String,
      default: null,
    },
   
    interest: {
      type: [String], // Change to an array of strings
      default: null,
      set: (val) => {
        // If the value is not provided or is an empty array, set it to null
        if (!val || (Array.isArray(val) && val.length === 0)) {
          return null;
        }
        // If duplicates exist, filter them out
        return [...new Set(val)];
      },
    },
    clubId: {
      type: String,
      default: null,
    },
    images: {
      type: [spotProfileImageSchema],
      default: null,
    },
    maxBench: {
      type: Number,
      default: 0,
    },
    maxSquat: {
      type: Number,
      default: 0,
    },
    weightType: {
      type: String,
      enum: ['lb', 'kg'],
      default: 'lb',
    },
    location: {
      type: {
        type: String,
        enum: ['Point'],
      },
      coordinates: [Number],
    },
    ghostModeOn: {
      type: Boolean,
      default: false,
    },
    spotNot: {
      type: [{ userId: String, expireDate: Date }],
      default: [],
      _id: false,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

spotProfileSchema.index({ location: '2dsphere' });
const spotProfileModel = model<SpotProfile>('spot-profile', spotProfileSchema);
export { spotProfileModel };
