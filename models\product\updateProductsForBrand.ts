import { SuccessResponse } from '../common/index';
import { Product } from './product';

/**
 * API Path: /admin/products/brand
 * method: PATCH
 * query: UpdateProductsForBrandQuery
 * response: UpdateProductsForBrandResponse
 */

export interface UpdateProductsForBrandRequest {
  productIds: string[];
  brandId: string;
}

export interface UpdateProductsForBrandSuccessResponse extends SuccessResponse {
  data: Product[];
}

export const enum UpdateProductsForBrandErrorMessages {
  CAN_NOT_UPDATE_PRODUCTS = 'Can not update products',
}
