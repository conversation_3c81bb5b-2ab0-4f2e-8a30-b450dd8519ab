import { SuccessResponse } from '../common/index';

/**
 * API Path: /spot/nearest-user-list
 * method: GET
 * query: FindNearestUserListQuery
 * response: FindNearestUserListResponse
 */

export enum NearestUserGenderType {
  FEMALE = 'female',
  MALE = 'male',
}

export interface FindNearestUserListQuery {
  latitude: number;
  longitude: number;
  offset: number;
  limit: number;
  gender?: string;
  interest?: string[];
  minAge?: number;
  maxAge?: number;
  maxDistance?: number;
  minSquat?: number;
  maxSquat?: number;
  minBench?: number;
  maxBench?: number;
  workoutType?: string;
  bodyType?: string;
  clubId?: string;
  ghostModeOn?: boolean;
  image?: string[];
}

export interface NearestUserList {
  id: string;
  name: string;
  gender?: string;
  maxBench?: number;
  maxSquat?: number;
  workoutType?: string;
  bodyType?: string;
  interest?: string;
  location?: {
    type: string;
    coordinates: number[];
  };
  image?: string[];
  age: number;
  distance: number;
}

export const enum FindNearestUserListErrorMessages {
  NEAREST_USER_NOT_FOUND = 'Nearest user not found',
}

export interface FindNearestUserListSuccessResponse extends SuccessResponse {
  data: NearestUserList[];
}
