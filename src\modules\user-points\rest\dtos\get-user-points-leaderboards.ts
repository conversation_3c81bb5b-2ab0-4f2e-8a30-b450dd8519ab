import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";
import { IsNotEmpty, IsObject } from "class-validator";
import { IUserLeadersboard } from "models";
import { ServiceSuccessResponse } from "src/helper/serviceResponse/service.response.interface";

export class IUserLeaderboardsResDto implements IUserLeadersboard {
  @ApiProperty()
  totalPoints: number;
  @ApiProperty()
  userId: string;
  @ApiProperty()
  name: string;
  @ApiProperty()
  image: string;
}


export class GetUserLeaderboardSuccessResponseDtoForUser implements ServiceSuccessResponse {

  @Expose()
  @ApiProperty({ required: true, type: [IUserLeaderboardsResDto] })
  @IsObject()
  @IsNotEmpty()
  data: [IUserLeaderboardsResDto];

}