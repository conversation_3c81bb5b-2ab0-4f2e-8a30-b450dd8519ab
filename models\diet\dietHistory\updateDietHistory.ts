import { SuccessResponse } from '../../common/successResponse';
import { DietHistory, FoodSource, MealType } from './dietHistory';

export interface UpdateDietHistoryRequestBody {
    userId?:string;
    foodId?: string;
    servingSize?: number;
    source?:FoodSource;
    consumedFoodId: string;
    mealType?:MealType;   
}


export interface UpdateDietHistorySuccessResponse extends SuccessResponse {
  data: DietHistory;
}

export const enum UpdateDietHistoryErrorMessage {
  CAN_NOT_UPDATE_CONSUMED_FOOD = 'Can not update consumed food',
  FOOD_NOT_EXISTS='Food does not exist',
  CAN_NOT_FIND_CONSUMED_FOOD='Can not find consumed food with diet history id of loggedIn user',
  CAN_NOT_UPDATE_FOODID='Can not update foodId without source(custom/global)'
}
