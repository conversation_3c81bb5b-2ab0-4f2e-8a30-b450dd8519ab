export const enum PostErrorEnum {
  PROVIDE_NECESSARY_CONTENT = 'Provide necessary content',
  ERROR_IN_CREATING_NEW_POST = 'Error in creating new post',
  POST_NOT_FOUND = 'Post not found',
  POST_CAN_NOT_BE_UPDATED = 'Post can not be updated',
  POST_CAN_NOT_BE_DELETED = 'Post can not be deleted',
  POST_LIST_NOT_FOUND = 'Post list not found',
  ERROR_IN_CREATING_NEW_POST_COMMENT = 'Error in creating new post comment',
  CAN_NOT_DO_THAT = 'Can not do that',
  ERROR_IN_SHARING_POST = 'Error in sharing post',
  CAN_NOT_SEE_POST = 'Can not see post',
  CAN_NOT_GET_USER_PREFERENCE = 'User not found or can not get user preference',
  USER_PROFILE_IS_LOCKED = 'User profile is locked',
}
