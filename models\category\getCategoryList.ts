import { SuccessResponse } from '../common/successResponse';
import { Photo } from './category';

export interface SubCategoryList {
  id: string;
  name: string;
  photo: Photo;
  published: boolean;
  displayOrder: number;
  slug: string;
  ancestors: GetCategoryListAncestor[];
  subCategories?: any[];
}

export interface NestedCategoryList {
  id: string;
  name: string;
  photo: Photo;
  published: boolean;
  displayOrder: number;
  slug: string;
  ancestors: GetCategoryListAncestor[];
  subCategories?: SubCategoryList[];
}

export interface GetCategoryList {
  categories: NestedCategoryList[];
}

export interface GetCategoryListAncestor {
  name: string;
  slug: string;
  level: number;
}

export interface GetCategoryListSuccessResponse extends SuccessResponse {
  data: GetCategoryList;
}

export const enum GetCategoryListErrorMessage {
  CAN_NOT_GET_CATEGORY_LIST = 'Can not get category list',
}
