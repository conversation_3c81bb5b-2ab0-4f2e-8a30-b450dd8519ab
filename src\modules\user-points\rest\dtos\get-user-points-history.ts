import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";
import { IsNotEmpty, IsObject } from "class-validator";
import { IUserPointsSources, IUserPointsTransHistory, IUserPointsTransTypeEnum } from "models";
import { ServiceSuccessResponse } from "src/helper/serviceResponse/service.response.interface";

export class IUserPointsHistoryResDto implements IUserPointsTransHistory {
  @ApiProperty({ required: false })
  id?: string;

  @ApiProperty()
  userId: string;

  @ApiProperty({ enum: IUserPointsTransTypeEnum })
  type: IUserPointsTransTypeEnum;

  @ApiProperty()
  points: number;

  @ApiProperty({ enum: IUserPointsSources, required: false })
  pointSourceType?: IUserPointsSources;

  @ApiProperty({ required: false })
  pointSourceId?: string;

  @ApiProperty({ required: false })
  pointSourceName?: string;

  @ApiProperty({ required: false })
  createdAt?: Date;
}




export class GetUserPointsHistorySuccessResponseDtoForUser implements ServiceSuccessResponse {

  @Expose()
  @ApiProperty({ required: true, type: [IUserPointsHistoryResDto] })
  @IsObject()
  @IsNotEmpty()
  data: [IUserPointsHistoryResDto];

}