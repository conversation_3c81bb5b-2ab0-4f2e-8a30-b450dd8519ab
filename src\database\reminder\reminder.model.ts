import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { <PERSON>minder } from 'src/entity/reminder';

const ReminderSchema = new Schema<Reminder>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
      index: true, // Admin ID who created the reminder
    },
    title: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    time: {
      type: String, // Time in 24-hour format (HH:MM)
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isRepeating: {
      type: Boolean,
      default: false,
    },
    repeatDays: {
      type: [String], // Array of days ['monday', 'tuesday', etc.]
      default: [],
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const ReminderModel = model<Reminder>('reminder', ReminderSchema);
export { ReminderModel };
