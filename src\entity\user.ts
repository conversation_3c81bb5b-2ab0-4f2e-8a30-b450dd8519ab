import { TagType } from 'models';

export class UserAddress {
  id?: string;
  firstName: string;
  lastName: string;
  addressLine1: string;
  addressLine2?: string;
  isDefault?: boolean;
  company?: string;
  city: string;
  state?: string;
  country?: string;
  postCode?: string;
  phone: string;
  tag?: TagType;
}
export class User {
  id?: string;
  name: string;
  phone?: string;
  email?: string;
  password?: string;
  addresses?: UserAddress[];
  fcmToken?: string;
  title?: string;
  organization?: string;
  workoutType?: string;
  bodyType?: string;
  dateOfBirth: Date;
  gender?: string;
  maxBench?: number;
  maxSquat: number;
  weightType:string;
  location?: {
    type: string;
    coordinates: number[];
  };
  image?: {
    profile: string;
  };
  isOnline: boolean;
  isDeleted: boolean;
  newsFeedStats: UserNewsFeedStats;
  totalBuddies: number;
  totalFollowers: number;
  coachId?: string;
}

// Newsfeed stats
export interface UserNewsFeedStats {
  userId: string;
  buddyListOffset: number;
  postDateOffset: number;
  followEePostListOffset: number;
  advertisingProductsOffset: number;
  totalBuddies: number;
  lastBuildAt?: Date;
}
