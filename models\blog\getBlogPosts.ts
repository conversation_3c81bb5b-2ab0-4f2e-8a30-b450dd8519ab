import { SuccessResponse } from '../common/index';
import { Blog } from './blog';

/**
 * API Path: /api/blogs
 * method: GET
 * response: GetAllBlogsResponse
 */

export enum BlogFilter {
  ALL = 'ALL',
  CURRENT_MONTH = 'CURRENT_MONTH',
}
export interface GetAllBlogPostsQuery {
  offset?: number;
  limit?: number;
  filter?: BlogFilter;
}
export interface GetAllBlogPostsSuccessResponse extends SuccessResponse {
  data: Blog[];
}

export const enum GetAllBlogPostsErrorMessages {
  CAN_NOT_GET_ALL_BLOG_POSTS = 'Can not get all blog posts',
}
