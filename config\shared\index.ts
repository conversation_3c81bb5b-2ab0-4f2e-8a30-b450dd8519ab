const {
  STORY_EXPIRATION_TIME,
  FETCH_STORIES_DEFAULT_OFFSET,
  FETCH_STORIES_DEFAULT_LIMIT,
  FETCH_STORIES_MAX_LIMIT,
  FETCH_STORIES_MAX_STORY_LIMIT_PER_USER,
  ONE_TO_ONE_CHAT_DEFAULT_SKIP,
  ONE_TO_ONE_CHAT_DEFAULT_LIMIT,
  ONE_TO_ONE_CHAT_MAX_LIMIT,
  CHAT_MAX_CONTENT_LENGTH,
  MAX_NUMBER_OF_USER_IN_CHAT_GROUP,
  DEFAULT_MAX_LIMIT,
  DEFAULT_LIMIT,
  DEFAULT_SKIP,
  DEFAULT_TITLE_LENGTH,
  DEFAULT_DESCRIPTION_LENGTH,
  CH<PERSON>LENGE_DURATION,
  ENV,
  PACKAGE_PROFILE_VIEW_LIMIT,
  PACKAGE_SPOT_REQUEST_LIMIT,
  FREE_TRIAL_PACKAGE_ID,
  SPOT_NOT_EXPIRE_AFTER_IN_MS,
  WITHDRAW_AMOUNT_SPLIT_RATIO,
  MINIMUM_WITHDRAW_AMOUNT
} = process.env;

export const sharedConfig = {
  storyExpirationTime: parseInt(STORY_EXPIRATION_TIME) || 1000 * 60 * 60 * 24,
  fetchStoriesDefaultOffset: parseFloat(FETCH_STORIES_DEFAULT_OFFSET) || 0,
  fetchStoriesDefaultLimit: parseInt(FETCH_STORIES_DEFAULT_LIMIT) || 10,
  fetchStoriesMaxLimit: parseInt(FETCH_STORIES_MAX_LIMIT) || 50,
  fetchStoriesMaxStoryLimitPerUser:
    parseInt(FETCH_STORIES_MAX_STORY_LIMIT_PER_USER) || 10,
  oneToOneChatDefaultSkip: parseInt(ONE_TO_ONE_CHAT_DEFAULT_SKIP) || 0,
  oneToOneChatDefaultLimit: parseInt(ONE_TO_ONE_CHAT_DEFAULT_LIMIT) || 20,
  oneToOneChatMaxLimit: parseInt(ONE_TO_ONE_CHAT_MAX_LIMIT) || 50,
  chatMaxContentLength: parseInt(CHAT_MAX_CONTENT_LENGTH) || 1000,
  chatGroupUserMaxLimit: parseInt(MAX_NUMBER_OF_USER_IN_CHAT_GROUP) || 250,

  defaultSkip: parseInt(DEFAULT_SKIP) || 0,
  defaultLimit: parseInt(DEFAULT_LIMIT) || 20,
  defaultMaxLImit: parseInt(DEFAULT_MAX_LIMIT) || 50,

  defaultTitleLength: parseInt(DEFAULT_TITLE_LENGTH) || 100,
  defaultDescriptionLenght: parseInt(DEFAULT_DESCRIPTION_LENGTH) || 1000,

  challengeDuration: parseInt(CHALLENGE_DURATION) || 2592000000,
  processENV: ENV || 'LOCAL',

  defaultPackageProfileViewLimit: parseInt(PACKAGE_PROFILE_VIEW_LIMIT) || 20,
  defaultPackageSpotRequestLimit: parseInt(PACKAGE_SPOT_REQUEST_LIMIT) || 20,
  freeTrialPackageId:
    FREE_TRIAL_PACKAGE_ID || 'f0e91f6f-e36e-44ca-8c1d-853310381bdb',
  spotNotExipresAfterInMs:
    parseInt(SPOT_NOT_EXPIRE_AFTER_IN_MS) || 5 * 60 * 1000, // default after 5 min
  
  coachWithdrawSplitRatio: parseInt(WITHDRAW_AMOUNT_SPLIT_RATIO) || 30,
  coachMinimumWithdrawAmount: parseInt(MINIMUM_WITHDRAW_AMOUNT) || 1000,
};
