import { SuccessResponse } from '../common/index';
import { User } from './user';

/**
 * API Path: /user/update-address/:addressId
 * method: PATCH
 * params: addressId
 * body: UserAddress
 * response: UpdateUserAddressResponse
 */

export interface UpdateUserAddressParams {
  addressId: string;
}

export interface UpdateUserAddressSuccessResponse extends SuccessResponse {
  data: User;
}

export const enum UpdateUserAddressErrorMessages {
  CAN_NOT_UPDATE_USER_ADDRESS = 'Can not update user address',
  USER_EMAIL_MATCH = 'User email match',
  USER_PHONE_MATCH = 'User phone number match',
}
