import { Post } from 'src/entity/post';
import { ProductPhoto } from 'src/entity/product';

type userInfo = {
  name: string;
  image?: {
    profile: string;
  };
};

type productInfo = {
  name: string;
  price: number;
  oldPrice: number;
  photos: ProductPhoto[];
  avgRating: number;
};

export type GetPostListWithUserInfoAndProductInfo = Omit<Post, 'weight'> & {
  userInfo: userInfo;
  comments: {
    userId: string;
    comment: string;
    createdAt: Date;
    userInfo: userInfo;
  }[];
  productInfo?: productInfo;
};

export type GetTimelineListWithUserInfo = Omit<Post, 'weight'> & {
  liked: true;
  userInfo: userInfo;
  comments: {
    userId: string;
    comment: string;
    createdAt: Date;
    userInfo: userInfo;
  }[];
};

export type GetPostListWithUserInfo = Omit<Post, 'weight'> & {
  userInfo: userInfo;
  likersInfo:{
    userId:string;
    name: string;
    image:{
      profile: string;
    };
  }[];
  comments: {
    userId: string;
    comment: string;
    createdAt: Date;
    userInfo: userInfo;
  }[];
};

export type GetPostListResponse = {
  data: GetPostListWithUserInfoAndProductInfo[];
};

export type GetTimelineListResponse = {
  data: GetTimelineListWithUserInfo[];
};
export type GetSinglePostResponse = { data: GetPostListWithUserInfo };
