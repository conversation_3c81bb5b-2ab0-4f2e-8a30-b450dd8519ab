import { Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { deepCasting } from 'src/internal/casting/object.casting';
import { throwNotFoundErr } from 'src/internal/exception/api.exception.ext';
import { REFERRAL_INFO_NOT_FOUND } from '../../common/const/referral.const';
import {
  getMultipleReferralCampaignSuccessResponseDto,
  GetReferralCampaignResponseDto,
  getSingleReferralCampaignSuccessResponseDto,
} from '../dtos/get-referral-campaign.dto';
import { ReferralCampaignRepositoryForUser } from '../repositories/user.repository';

@Injectable()
export class ReferralCampaignServiceForUser {
  constructor(
    private readonly campaignRepository: ReferralCampaignRepositoryForUser,
    private readonly helper: Helper,
  ) {}

  async getMultipleReferralCampaign(): Promise<getMultipleReferralCampaignSuccessResponseDto> {
    const newDocs = await this.campaignRepository.getReferralCampaigns();
    throwNotFoundErr(
      !newDocs,
      'Referral Campaigns not found!',
      REFERRAL_INFO_NOT_FOUND,
    );

    const responseDto = newDocs.map((item) =>
      deepCasting(GetReferralCampaignResponseDto, item),
    );
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getSingleReferralCampaign(
    campaignId: string,
  ): Promise<getSingleReferralCampaignSuccessResponseDto> {
    const referralDoc = await this.campaignRepository.getReferralCampaign(
      campaignId,
    );
    throwNotFoundErr(
      !referralDoc,
      'Referral Campaign not found!',
      REFERRAL_INFO_NOT_FOUND,
    );

    const responseDto = deepCasting(
      GetReferralCampaignResponseDto,
      referralDoc,
    );
    return this.helper.serviceResponse.successResponse(responseDto);
  }
}
