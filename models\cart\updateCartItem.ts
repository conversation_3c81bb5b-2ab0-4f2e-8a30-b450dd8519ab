import { SuccessResponse } from '../common/successResponse';
import { Cart } from './cartCommon/cart';

export interface UpdateCartItemRequest {
  productId?: string;
  quantity?: number;
  size?: string;
  color?: string;
}

export interface UpdateCartItemSuccessResponse extends SuccessResponse {
  data: Cart;
}

export const enum UpdateCartItemErrorMessage {
  CAN_NOT_UPDATE_CART_ITEM = 'Can not update cart item',
  CAN_NOT_REMOVE_CART_ITEM = 'Can not remove cart item',
}
