import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";
import { IsNotEmpty, IsObject } from "class-validator";
import { IUserPoints } from "models";
import { ServiceSuccessResponse } from "src/helper/serviceResponse/service.response.interface";

export class IUserPointsResDto implements IUserPoints {
  @ApiProperty()
  points: number;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  userRanking: number;

  @ApiProperty()
  totalRanking: number;
}


export class GetUserPointsSuccessResponseDtoForUser implements ServiceSuccessResponse {

  @Expose()
  @ApiProperty({ required: true, type: [IUserPointsResDto] })
  @IsObject()
  @IsNotEmpty()
  data: [IUserPointsResDto];

}