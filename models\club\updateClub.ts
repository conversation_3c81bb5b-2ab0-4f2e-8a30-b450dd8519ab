import { SuccessResponse } from '../common/index';
import { Club, ClubLocationType } from './club';

/**
 * API Path: /api/clubs/{id}
 * method: PATCH
 * body: UpdateClubRequestBody
 * response: UpdateClubResponse
 */
export interface UpdateClubParamsBody {
  id: string;
}

export interface UpdateImage {
  cover?: string;
  logo?: string;
}

export interface UpdateLocation {
  type?: ClubLocationType;
  coordinates?: number[];
}

export interface UpdateAddress {
  id?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  country?: string;
  postCode?: string;
}
export interface UpdateClubRequestBody {
  name?: string;
  image?: UpdateImage;
  location?: UpdateLocation;
  address?: UpdateAddress;
  description?: string;
}

export interface UpdateClubSuccessResponse extends SuccessResponse {
  data: Club;
}

export const enum UpdateClubErrorMessages {
  CAN_NOT_FIND_CLUB_WITH_THIS_ID = 'Can not find club with this id',
  CAN_NOT_UPDATE_CLUB = 'Can not update club',
}
