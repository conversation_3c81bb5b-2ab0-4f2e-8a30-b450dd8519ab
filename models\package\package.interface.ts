export enum DurationUnitEnum {
  MONTHS = 'Months',
  DAYS = 'Days',
}
export interface ISubscriptionPackage {
  id?: string;
  name: string;
  type: string;
  title: string;
  description: string;
  price: number;
  duration: number;
  durationUnit: DurationUnitEnum;
  durationInDays: number;
  currency: string;
  currencySymbol?: string;
  isActive: boolean;
}

export enum PackageErrorEnum {
  CAN_NOT_CREATE = 'Can not create',
  CAN_NOT_UPDATE = 'Can not update',
  PACKAGE_NOT_FOUND = 'Package not found',
  PACKAGE_NAME_ALREADY_EXIST = 'Package name is already exist',
}
