import { SuccessResponse } from 'models/common';
import { IIntermediateTraining } from './intermediateTraining';

/**
 * API Path: /api/IntermediateTraining/intermediate/{id}
 * method: DELETE
 * Param: DeleteIntermediateTrainingParam
 * response: DeleteIntermediateTrainingResponse
 */

export class DeleteIntermediateTrainingParam {
  id: string;
}

export interface DeleteIntermediateTrainingSuccessResponse
  extends SuccessResponse {
  data: IIntermediateTraining;
}

export type DeleteIntermediateTrainingResponse =
  DeleteIntermediateTrainingSuccessResponse;
