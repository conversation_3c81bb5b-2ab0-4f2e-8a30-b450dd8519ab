import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ReminderService } from './reminder.service';

@Injectable()
export class ReminderSchedulerService implements OnModuleInit {
  constructor(private readonly reminderService: ReminderService) {}

  onModuleInit() {
    // On startup, schedule all existing active reminders
    this.scheduleAllExistingReminders();
  }

  /**
   * Load and schedule all active reminders on module initialization
   */
  async scheduleAllExistingReminders() {
    console.log('Scheduling all existing active reminders...');
    await this.reminderService.scheduleAllActiveReminders();
  }

  /**
   * Fallback cron job that runs every 5 minutes to check for any missed reminders
   * This acts as a safety net in case of system downtime or queue failures
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async checkMissedReminders() {
    console.log('Checking for missed reminders...');
    await this.reminderService.sendReminderNotifications();
  }
}
