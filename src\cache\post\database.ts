import { redisCacheConfig } from 'config/cache';
import {
  FitBuddiesStatus,
  FitBuddiesType,
  GetPostListWithUserInfo,
} from 'models';
import { FitBuddiesModel } from 'src/database/fitBuddies/fidBuddies.model';
import { PostModel } from 'src/database/post/post.model';
import { PostCommentModel } from 'src/database/post/postComment.model';
import { PostReactionModel } from 'src/database/post/postReactions.model';
import { AdvertisingProductModel } from 'src/database/product/advertising-product.model';
import { UserModel } from 'src/database/user/user.model';
import { UserNewsFeedStatsModel } from 'src/database/user/userStats.model';
import { FitBuddies } from 'src/entity/fitBuddies';
import { Post, PostComment, PostReactionsType } from 'src/entity/post';
import { AdvertisingProduct } from 'src/entity/product';
import { User } from 'src/entity/user';
import { S3FileUploadService } from 'src/helper/s3Service';
import { PostIdsObj } from '.';
import { PipelineStage } from 'mongoose';
import { postConfig } from 'config/post';
class S3FileUploadHelperService extends S3FileUploadService {}

export async function findBuddyIds(
  userId: string,
  offset: number,
  fromDate: Date,
  toDate: Date,
): Promise<{ fitBuddyIds: string[]; followEeBuddyIds: string[] } | null> {
  try {
    let fitBuddyIds = [],
      followEeBuddyIds = [];

    const fitBuddyMatchObj = {
      $match: {
        userId,
        status: FitBuddiesStatus.ACTIVE,
        type: FitBuddiesType.FIT_BUDDY,
      },
    };

    const followEeMatchObj = {
      $match: {
        fitBuddyId: userId,
        status: FitBuddiesStatus.ACTIVE,
        type: FitBuddiesType.FOLLOWER,
      },
    };

    const newAffinityAddFieldsObj = {
      $addFields: {
        affinity: {
          $map: {
            input: '$affinity',
            as: 'aff',
            in: {
              $cond: {
                if: {
                  $and: [
                    {
                      $lte: ['$$aff.date', toDate],
                    },
                    {
                      $gte: ['$$aff.date', fromDate],
                    },
                  ],
                },
                then: {
                  $mergeObjects: [
                    '$$aff',
                    {
                      new_Score: '$$aff.score',
                    },
                  ],
                },
                else: '$$aff',
              },
            },
          },
        },
      },
    };

    const avgScoreObj = {
      $addFields: {
        avgScore: {
          $cond: {
            if: {
              $ne: [
                {
                  $avg: '$affinity.new_Score',
                },
                null,
              ],
            },
            then: {
              $avg: '$affinity.new_Score',
            },
            else: 0,
          },
        },
      },
    };

    const [buddies, followEes] = await Promise.all([
      await FitBuddiesModel.aggregate([
        { ...fitBuddyMatchObj },
        { ...newAffinityAddFieldsObj },
        { ...avgScoreObj },
        { $sort: { avgScore: -1 } },
        { $skip: Number(offset) },
        { $limit: Number(redisCacheConfig.buddy_default_offset) },
      ]),

      await FitBuddiesModel.aggregate([
        { ...followEeMatchObj },
        { ...newAffinityAddFieldsObj },
        { ...avgScoreObj },
        { $sort: { avgScore: -1 } },
        { $skip: Number(offset) },
        { $limit: Number(redisCacheConfig.followEe_default_offset) },
      ]),
    ]);

    fitBuddyIds = [...buddies.map((buddy) => buddy.fitBuddyId)];
    followEeBuddyIds = [...followEes.map((followEe) => followEe.userId)];

    return {
      fitBuddyIds,
      followEeBuddyIds,
    };
  } catch (error) {
    console.log(error.message);
    return {
      fitBuddyIds: [],
      followEeBuddyIds: [],
    };
  }
}

export async function findFitBuddiesPosts(
  fitBuddyIds: string[],
  fromDate: Date,
  toDate: Date,
): Promise<Post[] | null> {
  try {
    return await PostModel.find({
      userId: { $in: fitBuddyIds },
      privacy: { $ne: 'PRIVATE' },
      createdAt: { $gte: fromDate, $lte: toDate },
    })
      .sort({ weight: -1 })
      .lean();
  } catch (error) {
    console.log(error.message);
    return null;
  }
}

export async function findFitFollowEePosts(
  followEeBuddyIds: string[],
  fromDate: Date,
  toDate: Date,
): Promise<Post[] | null> {
  try {
    return await PostModel.find({
      userId: { $in: followEeBuddyIds },
      privacy: 'PUBLIC',
      createdAt: { $gte: fromDate, $lte: toDate },
    })
      .sort({ createdAt: -1, weight: -1 })
      .limit(redisCacheConfig.followEe_posts_default_limit)
      .lean();
  } catch (error) {
    console.log(error.message);
    return null;
  }
}

export async function findPopularPosts(
  fromDate: Date,
  toDate: Date,
  blockedIds: string[],
  fitBuddyIds?: string[],
  followEeBuddyIds?: string[],
): Promise<Post[] | null> {
  try {
    const doc = await PostModel.find({
      userId: { $nin: [...blockedIds, ...fitBuddyIds, ...followEeBuddyIds] },
      privacy: 'PUBLIC',
      createdAt: { $gte: fromDate, $lte: toDate },
    })
      .sort({ createdAt: -1, weight: -1 })
      .limit(redisCacheConfig.popular_posts_default_limit)
      .lean();
    return doc;
  } catch (error) {
    console.log(error.message);
    return null;
  }
}

function getPipelinePostRecation(postId: string) {
  const allowedReactions = [
    'LIKE',
    'LOVE',
    'FIRE',
    'FLEX',
    'RUN',
    'DUMBBELL',
    'WATER',
    'SWEAT',
    'GOAL',
    'HEART',
    'THUMBS_UP',
    'CLAP',
    'SURPRISED',
    'FIST',
    'FACE_ASTONISHED',
    'HIGH_FIVE',
    'FLAG',
    'GREEN_HEART'




  ];
  return [
    {
      $match: {
        postId: { $eq: postId },
        // type: { $eq: 'LIKE' },
        type: { $in: allowedReactions },
      },
    },
    {
      $limit: postConfig.likersInfoLimit,
    },
    {
      $lookup: {
        from: 'users',
        localField: 'userId',
        foreignField: 'id',
        as: 'likerInfo',
      },
    },
    {
      $project: {
        id: 1,
        _id: 0,
        userId: 1,
        name: {
          $first: '$likerInfo.name',
        },
        image: {
          $first: '$likerInfo.image',
        },
      },
    },
  ];
}

export async function getSinglePost(
  postId: string,
): Promise<(Post & Partial<GetPostListWithUserInfo>) | null> {
  try {
    const post = await PostModel.findOne(
      {
        id: postId,
      },
      { _id: 0 },
    ).lean();

    const stage = getPipelinePostRecation(postId);

    const singlePost = {
      ...post,
      likersInfo: (await PostReactionModel.aggregate(stage)) || [],
      userInfo: (await UserModel.findOne(
        { id: post.userId },
        { name: 1, image: 1, _id: 0 },
      ).lean()) || {
        name: null,
        image: {
          profile: null,
        },
      },
    };
    return singlePost;
  } catch (error) {
    console.log(error.message);
    return null;
  }
}

export async function getAdvertisingProducts(
  offset?: number,
): Promise<AdvertisingProduct[] | null> {
  try {
    return await AdvertisingProductModel.find({}, { productId: 1, _id: 0 })
      .skip(Number(offset || 0))
      .limit(Number(redisCacheConfig.advertising_products_default_limit))
      .lean();
  } catch (error: any) {
    console.log(error.message);
    return null;
  }
}

export async function getAdvertisingProductsCount(): Promise<number | 0> {
  try {
    return await AdvertisingProductModel.find({}).count().lean();
  } catch (error: any) {
    console.log(error.message);
    return 0;
  }
}

export async function updateUserNewsFeedStats<T>(
  userId: string,
  data: T,
): Promise<FitBuddies[] | null> {
  try {
    return await UserNewsFeedStatsModel.findOneAndUpdate(
      { userId },
      { $set: data },
    ).lean();
  } catch (error) {
    console.log(error.message);
    return null;
  }
}

function getPostPipeline(userId: string, postIds: string[]) {
  const allowedReactions = [
    'LIKE',
    'LOVE',
    'FIRE',
    'FLEX',
    'RUN',
    'DUMBBELL',
    'WATER',
    'SWEAT',
    'GOAL',
    'HEART',
    'THUMBS_UP',
    'CLAP',
    'SURPRISED',
    'FIST',
    'FACE_ASTONISHED',
    'HIGH_FIVE',
    'FLAG',
    'GREEN_HEART'
  ];
  const aggregatePipeLine: PipelineStage[] = [
    {
      $match: {
        id: { $in: postIds },
      },
    },
    {
      $lookup: {
        from: 'post-reactions',
        localField: 'id',
        foreignField: 'postId',
        pipeline: [
          {
            $sort: { createdAt: -1 },
          },
          {
            $addFields: {
              isUserIdExist: {
                $in: ['$userId', [userId]],
              },
            },
          },
          {
            $limit: postConfig.likersInfoLimit,
          },
          {
            $lookup: {
              from: 'users',
              localField: 'userId',
              foreignField: 'id',
              as: 'likerInfo',
            },
          },
          {
            $project: {
              id: 1,
              _id: 0,
              userId: 1,
              isUserIdExist: 1,
              name: {
                $first: '$likerInfo.name',
              },
              image: {
                $first: '$likerInfo.image',
              },
            },
          },
        ],
        as: 'likersInfo',
      },
    },
    
    // {
    //   $addFields: {
    //     liked: {
    //       $cond: {
    //         if: {
    //           $in: [true, '$likersInfo.isUserIdExist'],
    //         },
    //         then: true,
    //         else: false,
    //       },
    //     },
    //   },
    // },

   

    {
      $lookup: {
        from: 'post-reactions',
        let: { postId: '$id', userIdVar: userId },  // Pass postId and userId as variables
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$postId', '$$postId'] },  // Match the postId
                  { $eq: ['$userId', '$$userIdVar'] },  // Match the userId
                ],
              },
            },
          },
          {
            $project: {
              _id: 0,
              type: 1,  // Only return the reaction type (if any)
            },
          },
        ],
        as: 'userReaction',  // Store the result in 'userReaction'
      },
    },
    {
      $addFields: {
        liked: {
          $cond: {
            if: { $gt: [{ $size: '$userReaction' }, 0] },  // If the userReaction array has any elements
            then: true,  // The user has reacted (liked)
            else: false,  // The user hasn't reacted (liked)
          },
        },
      },
    },


    {
      $lookup: {
        from: 'post-reactions',
        let: { postId: '$id', userIdVar: userId },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$postId', '$$postId'] },  // Match postId
                  { $eq: ['$userId', '$$userIdVar'] },  // Match userId
                ],
              },
            },
          },
          {
            $project: {
              _id: 0,
              type: 1,  // Only return the reaction type
            },
          },
        ],
        as: 'userReaction',
      },
    },
    // 5. Add reactionType from userReaction lookup
    {
      $addFields: {
        // reactionType: { $arrayElemAt: ['$userReaction.type', 0] },  
        reactionType: {
          $ifNull: [{ $arrayElemAt: ['$userReaction.type', 0] }, null], // Null if no reaction
        },
      },
    },
    {
      $lookup: {
        from: 'post-comments',
        localField: 'id',
        foreignField: 'postId',
        pipeline: [
          {
            $sort: { createdAt: -1 },
          },
          {
            $limit: postConfig.commentsLimit,
          },
          {
            $lookup: {
              from: 'users',
              localField: 'userId',
              foreignField: 'id',
              as: 'commenterInfo',
            },
          },
          {
            $project: {
              id: 1,
              _id: 0,
              userId: 1,
              comment: 1,
              createdAt: 1,
              userInfo: {
                id: {
                  $first: '$commenterInfo.id',
                },
                name: {
                  $first: '$commenterInfo.name',
                },
                image: {
                  $first: '$commenterInfo.image',
                },
              },
            },
          },
        ],
        as: 'comments',
      },
    },

    {
      $lookup: {
        from: 'post-reactions',
        localField: 'id',
        foreignField: 'postId',
        pipeline: [
          {
            $match: { type: { $in: allowedReactions } }, // Filter based on allowed reaction types
          },
          {
            $group: {
              _id: '$type', // Group by reaction type
              users: { $addToSet: '$userId' }, // Collect users for each reaction type
            },
          },
          {
            $lookup: {
              from: 'users',
              localField: 'users',
              foreignField: 'id',
              as: 'reactionUsers', // Get user details for each reaction type
            },
          },
          {
            $project: {
              type: '$_id', // Rename _id to type (reaction type)
              _id: 0,
              users: {
                $map: {
                  input: '$reactionUsers',
                  as: 'user',
                  in: {
                    id: '$$user.id',
                    name: '$$user.name',
                    image: '$$user.image',
                  },
                },
              },
            },
          },
        ],
        as: 'reactionTypeUserInfo', // Store reaction details in the result
      },
    },

    {
      $lookup: {
        from: 'post-reactions',
        localField: 'id',
        foreignField: 'postId',
        pipeline: [
          {
            $sort: { createdAt: -1 },
          },
          {
            $addFields: {
              isUserIdExist: {
                $in: ['$userId', [userId]],
              },
            },
          },
          {
            $limit: postConfig.likersInfoLimit,
          },
          {
            $lookup: {
              from: 'users',
              localField: 'userId',
              foreignField: 'id',
              as: 'totalReactionerInfo',
            },
          },
          {
            $project: {
              id: 1,
              _id: 0,
              userId: 1,
              isUserIdExist: 1,
              name: {
                $first: '$totalReactionerInfo.name',
              },
              image: {
                $first: '$totalReactionerInfo.image',
              },
            },
          },
        ],
        as: 'totalReactionerInfo',
      },
    },
    {
      $addFields: {
        postId: '$id',
      },
    },
    {
      $project: {
        _id: 0,
        id: 0,
        userId: 0,
        weight: 0,
        images: 0,
        videos: 0,
        //id:0,
        location: 0,
        createdAt: 0,
        updatedAt: 0,
        content: 0,
        privacy: 0,
        type: 0,
      },
    },
  ];
  return aggregatePipeLine;
}

export async function postsAggregate(
  userId: string,
  postIds: string[],
  mappedPosts: Map<string, Post>,
): Promise<PostIdsObj[]> {
  try {
    const stage = getPostPipeline(userId, postIds);
    const posts = await PostModel.aggregate(stage);

   
    return posts;

    // const likedPosts = await PostReactionModel.find({
    //   userId,
    //   postId: { $in: [...postIds] },
    //   type: PostReactionsType.LIKE,
    // }).lean();

    // const map = new Map<string, PostReaction>();
    // for (let i = 0, len = likedPosts.length; i < len; i++) {
    //   map.set(likedPosts[i].postId, likedPosts[i]);
    // }

    // const promises = [];
    // const posts: PostIdsObj[] = [];
    // for (let i = 0, len = postIds.length; i < len; i++) {
    //   promises.push(
    //     getPostComments({
    //       postId: postIds[i],
    //     }),
    //   );
    // }

    // const promisesResults = await Promise.all(promises);
    // promisesResults.forEach((result, index) => {
    //   posts.push({
    //     postId: postIds[index],
    //     liked: map.get(postIds[index]) ? true : false,
    //     comments: result,
    //     totalComments: mappedPosts.get(postIds[index])?.totalComments || 0,
    //     totalLikes: mappedPosts.get(postIds[index])?.totalLikes || 0,
    //     totalShares: mappedPosts.get(postIds[index])?.totalShares || 0,
    //   });
    // });
    // return posts;
  } catch (error) {
    console.log(error.message);
    return null;
  }
}

export async function getPostComments(
  query: Record<string, any>,
): Promise<PostComment[] | []> {
  try {
    const comments = await PostCommentModel.find(query)
      .sort({ createdAt: -1 })
      .limit(2)
      .select('-_id -postId -updatedAt')
      .lean();

    const users: User[] = await UserModel.find({
      id: { $in: [...comments.map((comment) => comment.userId)] },
    })
      .select('-_id name image id')
      .lean();

    const map = new Map<string, User>();
    users?.length &&
      (await Promise.allSettled(
        users.map(async (user: User) => {
          map.set(user.id, user);
        }),
      ));

    return [
      ...comments.map((comment) => {
        return { ...comment, userInfo: map.get(comment.userId) };
      }),
    ];
  } catch (error) {
    console.log(error);
    return [];
  }
}

export async function blockList(userId: string): Promise<string[]> {
  const blockList = await FitBuddiesModel.find({
    $or: [
      { userId, status: 'BLOCKED' },
      { fitBuddyId: userId, status: 'BLOCKED' },
    ],
  }).lean();

  return [
    ...(blockList || []).map((e) => {
      if (e.userId === userId) {
        return e.fitBuddyId;
      } else {
        return e.userId;
      }
    }),
  ];
}
