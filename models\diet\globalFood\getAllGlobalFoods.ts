import { SuccessResponse } from '../../common/index';

/**
 * API Path: /api/diet/custom-foods
 * method: GET
 * response: GetAllCustomFoodsResponse
 */
export interface GetAllGlobalFoodsQuery {
  session?: string;
  search?: string;
}
export interface GetAllGlobalFoodsSuccessResponse extends SuccessResponse {
  data: [];
}

export const enum GetAllGlobalFoodsErrorMessages {
  CAN_NOT_GET_ALL_GLOBAL_FOODS= 'Can not get all global foods',
}
