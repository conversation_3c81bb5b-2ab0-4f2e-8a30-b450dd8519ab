import { randomUUID } from "crypto";
import { model, Schema } from "mongoose";
import { UserActivityStreak } from "src/entity/user-activity-streak";

const UserActivityStreakSchema = new Schema<UserActivityStreak>({
  id: {
    type: String,
    default: () => randomUUID(),
    index: true,
  },
  // User ID
  userId: {
    type: String,
    required: true,
  },
	// Current consecutive days
  currentStreak: {
    type: Number,
    default: 0,
  },
	// Last activity date
  lastActivityDate: {
    type: Date,
    default: Date.now,
  },
	// When current streak started
  streakStartDate: {
    type: Date,
    default: Date.now,
  },
	// Whether the streak is completed
  isCompleted: {
    type: Boolean,
    default: false,
  },
  // Whether the streak is current
  isCurrentStreak: {
    type: Boolean,
    default: false,
  },
},
{
  timestamps: true,
  versionKey: false,
});

const UserActivityStreakModel = model<UserActivityStreak>(
  'user-activity-streak',
  UserActivityStreakSchema,
);
export { UserActivityStreakModel };
