import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { ProductQuestionAnswer } from 'src/entity/product';

const ProductQuestionAnswerSchema = new Schema<ProductQuestionAnswer>(
  {
    id: {
      type: String,
      unique: true,
      default: () => randomUUID(),
    },
    productId: {
      type: String,
      required: true,
      index: true,
    },
    userId: {
      type: String,
      required: true,
    },
    question: {
      type: String,
      required: true,
    },
    answer: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const ProductQuestionAnswerModel = model<ProductQuestionAnswer>(
  'questions-answers',
  ProductQuestionAnswerSchema,
);
export { ProductQuestionAnswerModel };
