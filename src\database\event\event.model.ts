import { randomUUID } from 'crypto';
import { model, Schema } from "mongoose";
import { EventEntity, EventRegistrationFeeCurrency } from "src/entity/event";

/**
 * This is the schema for EventEntity. It will be used while creating the model of
 * EventEntity. The model name is EventEntityModel.
 */
const EventEntitySchema = new Schema<EventEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      default: null,
      required: false
    },
    media: {
      type: [String],
      default: null,
      required: false
    },
    isDeleted: {
      type: Boolean,
      default: false,
      required: true
    },
    startDate: {
      type: Date,
      default: null,
      required: false
    },
    endDate: {
      type: Date,
      default: null,
      required: false
    },
    registrationFee: {
      type: Number,
      default: 0,
      required: true
    },
    amountInCurrency: {
      type: String,
      enum: EventRegistrationFeeCurrency,
      default: EventRegistrationFeeCurrency.BDT,
      required: true
    },
    locationTitle: {
      type: String,
      default: null,
      required: false
    },
    actualLocation: {
      type: String,
      default: null,
      required: false
    },
    registrationStartTime: {
      type: Date,
      default: null,
      required: false
    },
    registrationEndTime: {
      type: Date,
      default: null,
      required: false
    }
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

/**
 * This is the model for EventEntity. You should use this model object if you want to 
 * perform operation related to event entity.
 */
const EventEntityModel = model<EventEntity>('event_entity', EventEntitySchema);
export { EventEntityModel };