import { SuccessResponse } from '../common/index';

/**
 * API Path: /api/posts/{id}
 * method: DELETE
 * params: DeleteClubParamsBody
 * response: DeleteClubResponse
 */


export const enum DeletePollSuccessMessages {
  POLL_DELETED_SUCCESSFULLY = 'Poll deleted successfully',
}

export const enum DeletePollErrorMessages {
  CAN_NOT_DELETE_POLL = 'Can not delete poll ',
  NO_POLL_EXIST = 'No poll exist',
}

export interface DeletePollSuccessResponse extends SuccessResponse {
  data: {
    message?: DeletePollSuccessMessages;
  };
}
