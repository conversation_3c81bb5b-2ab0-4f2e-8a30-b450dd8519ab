import { SuccessResponse } from '../common/index';

/**
 * API Path: /user-auth/google/sign-in
 * method: POST
 * body: GoogleUserSignInRequest
 * response: GoogleUserSignInResponse
 */

export interface GoogleUserSignInRequest {
  accessToken: string;
  fcmToken?: string;
}

export interface GoogleUserSignInSuccessResponse extends SuccessResponse {
  data: {
    token: string;
  };
}

export const enum GoogleUserSignInErrorMessages {
  NO_GOOGLE_USER_FOUND = 'No google user found',
  SERVER_ERROR = 'Server error',
}
