import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Poll, PollOption, PollStatus, PollType } from 'src/entity/poll';


const PollOptionSchema = new Schema<PollOption>(
  {
    id: {
      type:String,
      default: () => randomUUID(),
      unique: true,
  },
  title: {
    type: String,
    trim: true,
    required: false,
  },
  voteCount: {
    type: Number,
    default:0
  },
  description: {
    type: String,
    trim: true,
    
  },
  image:{type: String, default: null }


  }
)

const PollSchema = new Schema<Poll>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    pollType: {
      type:String,
      enum:PollType,
      required: false,
    },
    title: {
      type: String,
      trim: true,
      required: false,
    },
    subTitle: {
      type: String,
      trim: true,
      required: false,
    },
    description: {
      type: String,
      trim: true,
      required: false,
    },
    image:{type: String, default: null },
    status: {
      type: String,
      enum: PollStatus,
    },
    expirationDate: {
      type: Date,
      default: null,
    },
  
    options:{
      type:[PollOptionSchema],
      default:[]
    },
    winnerCount:{
      type:Number,
      default:0,
      required:false
    },
    winnerPollOptionId:{
      type:String,
      trim:true,
      required:false
    },
    isPinned:{
      type:Boolean,
      default: false,
      required:false,
    },
    winnerList:[
      {
        type: String, 
        index: true,
      },
    ],
 
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const PollModel = model<Poll>('poll', PollSchema);
export {PollModel };


