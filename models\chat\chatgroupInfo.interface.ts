export class IChatGroupMember {
  userId: string;
}

export interface ICreateChatGroup {
  id?: string;
  name?: string;
  description?: string;
  image?: string;
  members?: IChatGroupMember[];
  admins?: IChatGroupMember[];
  createdAt?: string;
  updatedAt?: string;
}

export interface ICreateChatGroupReq {
  id?: string;
  name?: string;
  description?: string;
  image?: string;
  members?: IChatGroupMember[];
}

export interface ICreateChatGroupRes {
  id?: string;
  name?: string;
  description?: string;
  image?: string;
  members?: IChatGroupMember[];
}

export interface IChatGroupMemberRes {
  id: string;
  name: string;
  image: string;
}
