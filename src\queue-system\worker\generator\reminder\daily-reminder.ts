import { Injectable, Module, OnApplicationBootstrap } from '@nestjs/common';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName } from 'src/queue-system/predefined-data';
import { NotificationRequestEvent } from 'src/queue-system/types';
import { CommonNotificationGeneratorQueue } from '../common/common-notification';

@Injectable()
export class DailyReminderGeneratorQueue implements OnApplicationBootstrap {
  async onApplicationBootstrap() {
    try {
      await (
        await QueueInstance
      ).consume(NotificationQueueName.DAILY_REMINDER_GENERATOR_QUEUE);
    } catch (error) {
      console.log(error.message);
    }
  }

  static async generator(
    requestEvent: NotificationRequestEvent,
  ): Promise<void> {
    try {
      await CommonNotificationGeneratorQueue.generator(requestEvent);
    } catch (error: any) {
      console.error('Daily Reminder Generator Queue error:', error.message);
    }
  }
}

@Module({
  providers: [DailyReminderGeneratorQueue],
})
export class DailyReminderGeneratorModule {}
