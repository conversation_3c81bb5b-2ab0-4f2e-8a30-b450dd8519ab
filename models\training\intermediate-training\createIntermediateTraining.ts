import { BaseExercise } from 'models/base-exercise';
import { SuccessResponse } from 'models/common';
import { IDayType, IIntermediateTraining } from './intermediateTraining';

/**
 * API Path: /api/training/intermediate
 * method: POST
 * body: CreateIntermediateTrainingReqBody
 * response: CreateTrainingResponse
 */
export class CreateIntermediateTrainingReqBody {
  baseExercise: BaseExercise;
  programId: string;
  week: number;
  day: IDayType;
  duration: number;
}
export class CreateIntermediateTrainingRequest {
  trainingList: CreateIntermediateTrainingReqBody[];
}

export interface CreateIntermediateTrainingSuccessResponse
  extends SuccessResponse {
  data: IIntermediateTraining[];
}

export type CreateIntermediateTrainingResponse =
  CreateIntermediateTrainingSuccessResponse;
