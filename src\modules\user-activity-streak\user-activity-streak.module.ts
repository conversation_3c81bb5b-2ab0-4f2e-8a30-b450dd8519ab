import { RedisModule } from "@nestjs-modules/ioredis";
import { Module, forwardRef } from "@nestjs/common";
import { redisCacheConfig } from 'config/cache';
import { ChatModule } from "../chat/chat.module";
import { UserPointsModule } from "../user-points/user-points.module";
import { UserActivityStreakControllerForUser } from "./controllers/user.controller";
import { UserActivityStreakProvider } from "./providers/internal.provider";
import { UserActivityStreakRepository } from "./repositories/user-activity-streak.repository";
import { UserDailyActivityRepository } from "./repositories/user-daily-activity.repository";
import { UserActivityStreakService } from "./services/user-activity-streak.service";
 
@Module({
    imports: [
      RedisModule.forRoot({
        config: {
          url: redisCacheConfig.redis_url,
        },
      }),
      forwardRef(() => ChatModule),
      UserPointsModule,
    ],
  controllers: [UserActivityStreakControllerForUser],
  providers: [
    UserActivityStreakRepository,
    UserDailyActivityRepository,
    UserActivityStreakService,
    UserActivityStreakProvider,
  ],
  exports: [UserActivityStreakProvider],
})
export class UserActivityStreakModule {}