import { model, Schema } from 'mongoose';
import { PostComment } from 'src/entity/post';

const PostCommentSchema = new Schema<PostComment>(
  {
    userId: {
      type: String,
      index: true,
      required: true,
    },
    postId: {
      type: String,
      index: true,
      required: true,
    },
    comment: {
      type: String,
      trim: true,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const PostCommentModel = model<PostComment>('post-comment', PostCommentSchema);
export { PostCommentModel };
