import { SuccessResponse } from '../common/index';

/**
 * API Path: /api/posts/{id}
 * method: DELETE
 * params: DeleteClubParamsBody
 * response: DeleteClubResponse
 */


export const enum DeleteReelsSuccessMessages {
  REELS_DELETED_SUCCESSFULLY = 'Reels deleted successfully',
}

export const enum DeleteReelsErrorMessages {
  CAN_NOT_DELETE_REELS = 'Can not delete reels ',
  NO_REELS_EXIST = 'No reels exist',
}

export interface DeleteReelsSuccessResponse extends SuccessResponse {
  data: {
    message?: DeleteReelsSuccessMessages;
  };
}
