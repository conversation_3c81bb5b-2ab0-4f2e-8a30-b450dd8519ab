import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Helper } from "src/helper/helper.interface";
import { deepCasting } from "src/internal/casting/object.casting";
import { throwBadReqErrIf, throwNotFoundErr } from "src/internal/exception/api.exception.ext";
import { UserInfoProvider } from "src/modules/user/providers/user.provider";
import { CampaignProviderForUser } from "../../campaign/providers/user.provider";
import { CAMPAIGN_NOT_ACTIVE, CAMPAIGN_NOT_FOUND } from "../../common/const/referral.const";
import { ReferralCodeEntity, ReferralCodeStatus } from "../../common/entities/referral-code.entity";
import { generateReferralCode } from "../../common/utils/referral-code.util";
import { GetReferralCodeDto, ReferralCodeResponseDto, ReferralCodeSuccessResponseDto } from "../dtos/create-referral-code.dto";
import { ReferralCodeRepositoryForUser } from "../repositories/user.repository";

@Injectable()
export class ReferralCodeServiceForUser {
    constructor(
        private readonly referralCodeRepository: ReferralCodeRepositoryForUser,
        @Inject(forwardRef(() => UserInfoProvider))
        private readonly userInfoProvider: UserInfoProvider,
        @Inject(forwardRef(() => CampaignProviderForUser))
        private readonly campaignProvider: CampaignProviderForUser,
        private readonly helper: Helper,
        private readonly eventEmitter: EventEmitter2
    ) { }

 
    async getReferralCode(dto: GetReferralCodeDto): Promise<ReferralCodeSuccessResponseDto> {
        const { userId, campaignId } = dto;
      
        const campaign = await this.campaignProvider.getSingleCoachProgram(campaignId);
        throwNotFoundErr(!campaign, 'Referral Campaign not found!', CAMPAIGN_NOT_FOUND);
      
        if (!campaign.isActive || campaign.status !== 'ACTIVE') {
          throwBadReqErrIf(true, 'Referral Campaign is not Active!', CAMPAIGN_NOT_ACTIVE);
        }
      
        const existingCode = await this.referralCodeRepository.getLatestReferralCodeByUserAndCampaign(userId, campaignId);
      
        const isCodeValid =
          existingCode &&
          existingCode.isActive &&
          (!existingCode.expiresAt || new Date(existingCode.expiresAt) > new Date()) &&
          (existingCode.usageLimit == null || existingCode.usageCount < existingCode.usageLimit);
      
        const code = isCodeValid
          ? existingCode
            : await this.createNewReferralCode(userId, campaign);
        
      
      
        const response = deepCasting(ReferralCodeResponseDto,
            {
          id: code.id,
          code: code.code,
          campaignId: campaign.id,
          campaignName: campaign.name,
          expiresAt: code.expiresAt,
          referrerPoints: campaign.baseRewardConfig.referrerPoints,
          refereePoints: campaign.baseRewardConfig.refereePoints,
            }
        );

       
      
        return this.helper.serviceResponse.successResponse(response);
      }

      private async createNewReferralCode(userId: string, campaign: any): Promise<ReferralCodeEntity> {
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + campaign.validityDays);
      
        const newCode: Partial<ReferralCodeEntity> = {
          code: generateReferralCode(),
          userId,
          campaignId: campaign.id,
          status: ReferralCodeStatus.ACTIVE,
          isActive: true,
          expiresAt,
          usageLimit: campaign.maxReferralCodeUse
        };
      
        const createdCode = await this.referralCodeRepository.createReferralCode(newCode);
      
        this.eventEmitter.emit('referral.code.created', {
          codeId: createdCode.id,
          userId,
          campaignId: campaign.id
        });
      
        return createdCode;
      }
      

}