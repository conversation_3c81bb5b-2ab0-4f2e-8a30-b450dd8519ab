import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Post, PostPrivacy, PostType } from 'src/entity/post';
import { PostMediaType } from './post.model';

const PendingPostSchema = new Schema<Partial<Post>>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      index: true,
      required: true,
    },
    type: {
      type: String,
      enum: PostType,
      default: PostType.OWN,
    },
    content: {
      type: String,
      trim: true,
      default: null,
    },
    images: PostMediaType,
    videos: PostMediaType,
    privacy: {
      type: String,
      enum: PostPrivacy,
      default: PostPrivacy.PUBLIC,
    },
    location: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

PendingPostSchema.add({ sharedPost: PendingPostSchema });
const PendingPostModel = model<Post>('pending-post', PendingPostSchema);
export { PendingPostModel };
