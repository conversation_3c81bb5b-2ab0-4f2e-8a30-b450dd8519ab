import { model, Schema } from 'mongoose';

import { ReelsReaction, ReelsReactionsType } from 'src/entity/reels';

const ReelsReactionSchema = new Schema<ReelsReaction>(
  {
    userId: {
      type: String,
      index: true,
      required: true,
    },
    reelsId: {
      type: String,
      index: true,
      required: true,
    },
    type: {
      type: String,
      trim: true,
      required: true,
      enum: ReelsReactionsType,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const ReelsReactionModel = model<ReelsReaction>(
  'reels-reaction',
  ReelsReactionSchema,
);
export { ReelsReactionModel };
