import { redisCacheConfig } from 'config/cache';
import { RedisClientType } from 'redis';
import { getRedisClient } from './redis.init';

export class RedisCacheHelper {
  /**
   * To save the data in Redis under the key name of the value.
   * @param key : string
   * @param data : T
   * @param expire_time : number(optional)
   * @returns Promise<string | null>
   */
  static async setData<T>(
    key: string,
    data: T,
    expire_time?: number,
  ): Promise<string | null> {
    try {
      const redisClient: RedisClientType = await getRedisClient();
      // console.log('key',key)
      // console.log("redis set data", JSON.stringify(data) )
      return await redisClient.set(key, JSON.stringify(data), {
        EX:
          !key.startsWith(redisCacheConfig.advertising_products_channel) &&
          (expire_time || redisCacheConfig.expire_time),
      });
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  /**
   * To save the data in Redis under the key name of the value.
   * @param key : string
   * @returns Promise<void>
   */
  static async deleteData(key: string): Promise<void> {
    try {
      const redisClient: RedisClientType = await getRedisClient();
      await redisClient.del(key);
      // console.log('Deleted cached data for ', key);
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  /**
   * Retrieve a key stored in Redis.
   * @param key : string
   * @returns Promise<T>
   */
  static async getData<T>(key: string): Promise<T> {
    // console.log('key', key)
    try {
      const redisClient: RedisClientType = await getRedisClient();
      return JSON.parse(await redisClient.get(key));
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  /**
   * Execute the redis command.
   * @param command : string[]
   * @returns Promise<T>
   */
  static async executeCommand<T>(command: string[]): Promise<T[] | null> {
    try {
      // console.log('command',command)
      const redisClient: RedisClientType = await getRedisClient();
      const results: any = await redisClient.sendCommand(command);
      // console.log('result',results)
      return results && results.length
        ? [...(results || []).map((result: string) => JSON.parse(result))]
        : null;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  /**
   * All Ids are pushed into the key. (left push)
   * @param key : string
   * @param Ids : T[]
   * @returns Promise<number>
   */
  static async leftPushData<T>(key: string, Ids: T[]): Promise<number> {
    try {
      const redisClient: RedisClientType = await getRedisClient();
      return await redisClient.lPush(key, [
        ...Ids.map((Id: T) => JSON.stringify(Id)),
      ]);
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  /**
   * All Ids are pushed into the key. (right push)
   * @param key : string
   * @param Ids : T[]
   * @returns Promise<number>
   */
  static async rightPushData<T>(key: string, Ids: T[]): Promise<number> {
    try {
      const redisClient: RedisClientType = await getRedisClient();
      return await redisClient.rPush(key, [
        ...Ids.map((Id: T) => JSON.stringify(Id)),
      ]);
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  /**
   * PostId is pushed into several keys. (left push)
   * @param userIds : string[]
   * @param postId : string
   * @returns Promise<void>
   */
  static async pushMultipleKeysData(
    userIds: string[],
    postIdObj: { postId: string },
  ): Promise<void> {
    // console.log('redis multiplekey userid',userIds)
    // console.log('redis postIdsObj',postIdObj)
    try {
      const redisClient: RedisClientType = await getRedisClient();
      await Promise.allSettled([
        ...userIds.map(
          async (userId) =>
            await redisClient.lPush(
              `${redisCacheConfig.user_feed_channel}/${userId}`,
              JSON.stringify(postIdObj),
            ),
        ),
      ]);
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }
}
